package com.weaver.seconddev.zyhlw.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * QueryWrapperSqlUtils 测试类
 * 测试优化后的泛型表名获取功能
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
class QueryWrapperSqlUtilsTest {

    @Test
    @DisplayName("测试通过setEntityClass设置实体类获取表名")
    void testGetTableName_WithSetEntityClass() {
        // 创建QueryWrapper并设置实体类
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntityClass(OutlayBudgetDO.class);
        queryWrapper.eq("id", "100830550000011749");
        queryWrapper.select("yu_ssyje");

        // 提取SQL
        QueryWrapperSqlUtils.SqlResult sqlResult = QueryWrapperSqlUtils.extractSql(queryWrapper);

        // 验证结果
        assertNotNull(sqlResult);
        assertNotNull(sqlResult.getSql());
        assertTrue(sqlResult.getSql().contains("uf_jing_fysgl"), 
                "SQL应该包含@TableName注解指定的表名: " + sqlResult.getSql());
        assertTrue(sqlResult.getSql().contains("yu_ssyje"), 
                "SQL应该包含选择的字段: " + sqlResult.getSql());
        
        System.out.println("生成的SQL: " + sqlResult.getSql());
        System.out.println("参数: " + sqlResult.getParams());
    }

    @Test
    @DisplayName("测试通过泛型匿名内部类获取表名")
    void testGetTableName_WithGenericAnonymousClass() {
        // 使用匿名内部类创建QueryWrapper，保留泛型信息
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<OutlayBudgetDO>() {};
        queryWrapper.eq("yu_szje", 1000.0f);
        queryWrapper.select("yu_ssyje", "yu_szje");

        try {
            // 提取SQL
            QueryWrapperSqlUtils.SqlResult sqlResult = QueryWrapperSqlUtils.extractSql(queryWrapper);

            // 验证结果
            assertNotNull(sqlResult);
            assertNotNull(sqlResult.getSql());
            assertTrue(sqlResult.getSql().contains("uf_jing_fysgl"), 
                    "SQL应该包含@TableName注解指定的表名: " + sqlResult.getSql());
            
            System.out.println("匿名内部类生成的SQL: " + sqlResult.getSql());
            System.out.println("参数: " + sqlResult.getParams());
            
        } catch (RuntimeException e) {
            // 如果无法从泛型获取，应该给出友好的错误信息
            assertTrue(e.getMessage().contains("建议使用"), 
                    "错误信息应该包含使用建议: " + e.getMessage());
            System.out.println("预期的错误信息: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试普通泛型创建方式的处理")
    void testGetTableName_WithNormalGeneric() {
        // 普通方式创建QueryWrapper（泛型信息会被擦除）
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("applicant", 1001L);

        try {
            // 提取SQL
            QueryWrapperSqlUtils.SqlResult sqlResult = QueryWrapperSqlUtils.extractSql(queryWrapper);

            // 如果成功，验证结果
            assertNotNull(sqlResult);
            System.out.println("普通泛型生成的SQL: " + sqlResult.getSql());
            
        } catch (RuntimeException e) {
            // 如果无法获取表名，应该给出友好的错误信息
            assertTrue(e.getMessage().contains("建议使用"), 
                    "错误信息应该包含使用建议: " + e.getMessage());
            System.out.println("预期的错误信息: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("测试手动指定表名的方式")
    void testGetTableName_WithManualTableName() {
        // 创建QueryWrapper
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("budget_no", "BUD001");
        queryWrapper.select("yu_ssyje", "yu_szje");

        // 使用手动指定表名的方法
        QueryWrapperSqlUtils.SqlResult sqlResult = QueryWrapperSqlUtils.extractSql(queryWrapper, "uf_jing_fysgl");

        // 验证结果
        assertNotNull(sqlResult);
        assertNotNull(sqlResult.getSql());
        assertTrue(sqlResult.getSql().contains("uf_jing_fysgl"), 
                "SQL应该包含手动指定的表名: " + sqlResult.getSql());
        
        System.out.println("手动指定表名生成的SQL: " + sqlResult.getSql());
        System.out.println("参数: " + sqlResult.getParams());
    }

    @Test
    @DisplayName("测试完整SQL提取功能")
    void testExtractCompleteSql() {
        // 创建QueryWrapper并设置实体类
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntityClass(OutlayBudgetDO.class);
        queryWrapper.eq("applicant", 1001L);
        queryWrapper.gt("yu_szje", 1000.0f);
        queryWrapper.select("yu_ssyje", "yu_szje", "applicant");

        // 提取完整SQL
        QueryWrapperSqlUtils.CompleteSqlResult completeSqlResult = 
                QueryWrapperSqlUtils.extractCompleteSql(queryWrapper);

        // 验证结果
        assertNotNull(completeSqlResult);
        assertNotNull(completeSqlResult.getSql());
        assertNotNull(completeSqlResult.getTableName());
        assertEquals("uf_jing_fysgl", completeSqlResult.getTableName());
        assertTrue(completeSqlResult.getSql().contains("SELECT"));
        assertTrue(completeSqlResult.getSql().contains("FROM"));
        assertTrue(completeSqlResult.getSql().contains("WHERE"));
        
        System.out.println("完整SQL: " + completeSqlResult.getSql());
        System.out.println("表名: " + completeSqlResult.getTableName());
        System.out.println("参数: " + completeSqlResult.getParams());
    }

    @Test
    @DisplayName("测试可执行SQL生成")
    void testGetExecutableSql() {
        // 创建QueryWrapper并设置实体类
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.setEntityClass(OutlayBudgetDO.class);
        queryWrapper.eq("applicant", 1001L);
        queryWrapper.like("yu_sd", "BUD");

        // 获取可执行SQL
        String executableSql = QueryWrapperSqlUtils.getExecutableSql(queryWrapper);

        // 验证结果
        assertNotNull(executableSql);
        assertFalse(executableSql.contains("?"), "可执行SQL不应该包含占位符");
        assertTrue(executableSql.contains("1001"), "可执行SQL应该包含参数值");
        // 注意：like查询的参数会被包装成 %BUD%，所以检查这个格式
        assertTrue(executableSql.contains("BUD") || executableSql.contains("%BUD%"),
                "可执行SQL应该包含字符串参数值");

        System.out.println("可执行SQL: " + executableSql);
    }
}
