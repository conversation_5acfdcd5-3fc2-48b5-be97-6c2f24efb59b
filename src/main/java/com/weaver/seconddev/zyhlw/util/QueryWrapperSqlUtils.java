package com.weaver.seconddev.zyhlw.util;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * QueryWrapper SQL工具类
 * 支持完整的SQL提取，包括SELECT、ORDER BY、GROUP BY等子句
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Slf4j
@Component
public class QueryWrapperSqlUtils {

    /**
     * 提取完整的SQL信息（包括SELECT、ORDER BY等）
     */
    public static <T> CompleteSqlResult extractCompleteSql(QueryWrapper<T> queryWrapper) {
        String tableName = getTableName(queryWrapper);

        // 获取各个SQL片段
        String selectSql = getSelectSql(queryWrapper);
        String fromSql = "FROM " + tableName;
        String whereSql = getWhereSql(queryWrapper);

        // 组装完整SQL
        StringBuilder completeSql = new StringBuilder();
        completeSql.append(selectSql).append(" ");
        completeSql.append(fromSql);

        if (StringUtils.isNotEmpty(whereSql)) {
            completeSql.append(" WHERE ").append(whereSql);
        }

        // 获取参数
        Map<String, Object> paramMap = queryWrapper.getParamNameValuePairs();
        List<Object> params = extractParamsInOrder(completeSql.toString(), paramMap);

        // 替换占位符
        String finalSql = completeSql.toString()
                .replaceAll("#\\{ew\\.paramNameValuePairs\\.[^}]+}", "?");

        return new CompleteSqlResult(finalSql, params, tableName);
    }

    /**
     * 提取基础SQL信息（兼容原有方法）
     */
    public static <T> SqlResult extractSql(QueryWrapper<T> queryWrapper) {
        CompleteSqlResult completeSqlResult = extractCompleteSql(queryWrapper);
        return new SqlResult(completeSqlResult.getSql(), completeSqlResult.getParams());
    }

    /**
     * 从QueryWrapper提取SQL和参数（指定表名）
     */
    public static SqlResult extractSql(QueryWrapper<?> queryWrapper, String tableName) {
        String sqlSegment = queryWrapper.getSqlSegment();
        Map<String, Object> paramMap = queryWrapper.getParamNameValuePairs();

        // 构建完整SQL
        StringBuilder sql = new StringBuilder("SELECT * FROM ").append(tableName);
        if (StringUtils.isNotEmpty(sqlSegment)) {
            sql.append(" WHERE ").append(sqlSegment);
        }

        // 替换参数占位符为?
        String finalSql = sql.toString();
        List<Object> params = new ArrayList<>();

        // 按照参数在SQL中出现的顺序提取参数值
        Pattern pattern = Pattern.compile("#\\{ew\\.paramNameValuePairs\\.(MPGENVAL\\d+)}");
        Matcher matcher = pattern.matcher(finalSql);

        while (matcher.find()) {
            String paramKey = matcher.group(1);
            params.add(paramMap.get(paramKey));
        }

        // 替换占位符为?
        finalSql = finalSql.replaceAll("#\\{ew\\.paramNameValuePairs\\.[^}]+}", "?");

        return new SqlResult(finalSql, params);
    }

    /**
     * 获取可直接执行的SQL（自动获取表名）
     */
    public static <T> String getExecutableSql(QueryWrapper<T> queryWrapper) {
        SqlResult sqlResult = extractSql(queryWrapper);
        return sqlResult.getExecutableSql();
    }

    /**
     * 获取可直接执行的SQL（指定表名）
     */
    public static String getExecutableSql(QueryWrapper<?> queryWrapper, String tableName) {
        SqlResult sqlResult = extractSql(queryWrapper, tableName);
        return sqlResult.getExecutableSql();
    }

    /**
     * 获取SELECT子句
     */
    private static String getSelectSql(QueryWrapper<?> queryWrapper) {
        String sqlSelect = queryWrapper.getSqlSelect();
        return StringUtils.isNotEmpty(sqlSelect) ? String.format("SELECT %s", sqlSelect) : "SELECT *";
    }

    /**
     * 获取WHERE子句
     */
    private static String getWhereSql(QueryWrapper<?> queryWrapper) {
        return queryWrapper.getSqlSegment();
    }

    /**
     * 按顺序提取参数
     */
    private static List<Object> extractParamsInOrder(String sql, Map<String, Object> paramMap) {
        List<Object> params = new ArrayList<>();
        Pattern pattern = Pattern.compile("#\\{ew\\.paramNameValuePairs\\.(MPGENVAL\\d+)}");
        Matcher matcher = pattern.matcher(sql);

        while (matcher.find()) {
            String paramKey = matcher.group(1);
            params.add(paramMap.get(paramKey));
        }

        return params;
    }

    /**
     * 从QueryWrapper中获取实体类的表名
     * 优先从泛型获取，提供多种获取方式和友好的错误信息
     */
    private static <T> String getTableName(QueryWrapper<T> queryWrapper) {
        log.debug("开始获取QueryWrapper的表名");

        try {
            // 方式1：优先从entityClass字段获取（支持泛型和setEntityClass设置）
            Class<T> entityClass = getEntityClassFromWrapper(queryWrapper);
            if (entityClass != null) {
                String tableName = getTableNameFromEntity(entityClass);
                log.debug("成功从实体类获取表名: {}", tableName);
                return tableName;
            }

            // 方式2：尝试从SQL中解析表名（备用方案）
            log.debug("无法从实体类获取表名，尝试从SQL中解析");
            return getTableNameFromWrapper(queryWrapper);

        } catch (Exception e) {
            log.error("获取表名失败", e);
            throw new RuntimeException("无法自动获取表名。建议使用 queryWrapper.setEntityClass(YourEntity.class) 设置实体类，或使用带表名参数的方法手动指定表名", e);
        }
    }

    /**
     * 从QueryWrapper中获取实体类
     * 支持通过泛型或setEntityClass方法设置的实体类
     * 优先从泛型获取，然后尝试从entityClass字段获取
     */
    private static <T> Class<T> getEntityClassFromWrapper(QueryWrapper<T> queryWrapper) {
        // 方式1：优先尝试从泛型信息获取
        Class<T> entityClass = getEntityClassFromGeneric(queryWrapper);
        if (entityClass != null) {
            log.debug("成功从泛型信息获取实体类: {}", entityClass.getName());
            return entityClass;
        }

        // 方式2：从entityClass字段获取（setEntityClass设置的）
        entityClass = getEntityClassFromField(queryWrapper);
        if (entityClass != null) {
            log.debug("成功从entityClass字段获取实体类: {}", entityClass.getName());
            return entityClass;
        }

        // 方式3：尝试从其他可能的字段获取
        entityClass = getEntityClassFromOtherFields(queryWrapper);
        if (entityClass != null) {
            log.debug("成功从其他字段获取实体类: {}", entityClass.getName());
            return entityClass;
        }

        log.debug("无法从QueryWrapper获取实体类信息");
        return null;
    }

    /**
     * 从泛型信息中获取实体类
     */
    @SuppressWarnings("unchecked")
    private static <T> Class<T> getEntityClassFromGeneric(QueryWrapper<T> queryWrapper) {
        try {
            // 获取QueryWrapper的类型
            Class<?> wrapperClass = queryWrapper.getClass();
            log.debug("QueryWrapper类型: {}", wrapperClass.getName());

            // 尝试从泛型超类获取
            Type genericSuperclass = wrapperClass.getGenericSuperclass();
            if (genericSuperclass instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

                if (actualTypeArguments.length > 0) {
                    Type firstTypeArg = actualTypeArguments[0];
                    if (firstTypeArg instanceof Class) {
                        Class<T> entityClass = (Class<T>) firstTypeArg;
                        log.debug("从泛型超类获取到实体类: {}", entityClass.getName());
                        return entityClass;
                    }
                }
            }

            // 尝试从泛型接口获取
            Type[] genericInterfaces = wrapperClass.getGenericInterfaces();
            for (Type genericInterface : genericInterfaces) {
                if (genericInterface instanceof ParameterizedType) {
                    ParameterizedType parameterizedType = (ParameterizedType) genericInterface;
                    Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();

                    if (actualTypeArguments.length > 0) {
                        Type firstTypeArg = actualTypeArguments[0];
                        if (firstTypeArg instanceof Class) {
                            Class<T> entityClass = (Class<T>) firstTypeArg;
                            log.debug("从泛型接口获取到实体类: {}", entityClass.getName());
                            return entityClass;
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.debug("从泛型信息获取实体类时发生异常: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 从entityClass字段获取实体类（setEntityClass设置的）
     */
    @SuppressWarnings("unchecked")
    private static <T> Class<T> getEntityClassFromField(QueryWrapper<T> queryWrapper) {
        try {
            // 通过反射获取QueryWrapper中的entityClass字段
            Field entityClassField = AbstractWrapper.class.getDeclaredField("entityClass");
            entityClassField.setAccessible(true);
            Class<T> entityClass = (Class<T>) entityClassField.get(queryWrapper);

            if (entityClass != null) {
                return entityClass;
            }

        } catch (NoSuchFieldException e) {
            log.debug("未找到entityClass字段，可能是MyBatis-Plus版本不兼容: {}", e.getMessage());
        } catch (Exception e) {
            log.debug("获取entityClass字段时发生异常: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 从其他可能的字段获取实体类
     */
    @SuppressWarnings("unchecked")
    private static <T> Class<T> getEntityClassFromOtherFields(QueryWrapper<T> queryWrapper) {
        try {
            // 获取所有字段，查找可能保存实体类信息的字段
            Class<?> wrapperClass = queryWrapper.getClass();
            while (wrapperClass != null && wrapperClass != Object.class) {
                Field[] fields = wrapperClass.getDeclaredFields();

                for (Field field : fields) {
                    field.setAccessible(true);
                    Object fieldValue = field.get(queryWrapper);

                    // 检查字段值是否为Class类型
                    if (fieldValue instanceof Class) {
                        Class<?> clazz = (Class<?>) fieldValue;
                        // 检查是否有@TableName注解，作为实体类的标识
                        if (clazz.isAnnotationPresent(TableName.class)) {
                            log.debug("从字段 {} 获取到实体类: {}", field.getName(), clazz.getName());
                            return (Class<T>) clazz;
                        }
                    }
                }

                wrapperClass = wrapperClass.getSuperclass();
            }

        } catch (Exception e) {
            log.debug("从其他字段获取实体类时发生异常: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 从实体类获取表名
     * 优先使用@TableName注解，否则将类名转换为下划线格式
     */
    private static <T> String getTableNameFromEntity(Class<T> entityClass) {
        log.debug("从实体类获取表名: {}", entityClass.getName());

        // 检查@TableName注解
        TableName tableNameAnnotation = entityClass.getAnnotation(TableName.class);
        if (tableNameAnnotation != null && StringUtils.isNotEmpty(tableNameAnnotation.value())) {
            String tableName = tableNameAnnotation.value();
            log.debug("使用@TableName注解指定的表名: {}", tableName);
            return tableName;
        }

        // 如果没有@TableName注解，使用类名转换为下划线格式
        String tableName = camelToUnderscore(entityClass.getSimpleName());
        log.debug("使用类名转换的表名: {} -> {}", entityClass.getSimpleName(), tableName);
        return tableName;
    }

    /**
     * 从QueryWrapper中尝试获取表名（备用方案）
     * 通过解析SQL语句获取表名
     */
    private static String getTableNameFromWrapper(QueryWrapper<?> queryWrapper) {
        log.debug("尝试从QueryWrapper的SQL中解析表名");

        try {
            // 尝试通过getTargetSql()方法获取
            String targetSql = queryWrapper.getTargetSql();
            log.debug("获取到的targetSql: {}", targetSql);

            if (StringUtils.isNotEmpty(targetSql)) {
                // 从SQL中提取表名（简单实现）
                Pattern pattern = Pattern.compile("FROM\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(targetSql);
                if (matcher.find()) {
                    String tableName = matcher.group(1);
                    log.debug("从SQL中解析到表名: {}", tableName);
                    return tableName;
                }
                log.debug("SQL中未找到FROM子句或表名格式不匹配");
            } else {
                log.debug("targetSql为空，无法解析表名");
            }
        } catch (Exception e) {
            log.warn("从SQL中解析表名时发生异常: {}", e.getMessage());
        }

        throw new RuntimeException("无法自动获取表名。请使用以下方式之一：\n" +
                "1. 使用 queryWrapper.setEntityClass(YourEntity.class) 设置实体类\n" +
                "2. 使用带表名参数的方法：extractSql(queryWrapper, \"your_table_name\")\n" +
                "3. 确保实体类有 @TableName 注解");
    }

    /**
     * 驼峰转下划线
     */
    private static String camelToUnderscore(String camelCase) {
        return camelCase.replaceAll("([a-z])([A-Z])", "$1_$2").toLowerCase();
    }

    /**
     * 格式化参数值
     */
    private static String formatParamValue(Object param) {
        if (param == null) {
            return "NULL";
        }
        if (param instanceof String) {
            return "'" + param.toString().replace("'", "''") + "'";
        }
        if (param instanceof Date) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return "'" + sdf.format((Date) param) + "'";
        }
        if (param instanceof LocalDateTime) {
            return "'" + ((LocalDateTime) param).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "'";
        }
        return param.toString();
    }

    /**
     * 完整SQL结果类
     */
    @Data
    @AllArgsConstructor
    public static class CompleteSqlResult {
        private String sql;
        private List<Object> params;
        private String tableName;

        /**
         * 获取可执行的SQL（参数已替换）
         */
        public String getExecutableSql() {
            String executableSql = sql;
            for (Object param : params) {
                String paramStr = formatParamValue(param);
                executableSql = executableSql.replaceFirst("\\?", paramStr);
            }
            return executableSql;
        }
    }

    /**
     * 基础SQL结果类（兼容性）
     */
    @Data
    @AllArgsConstructor
    public static class SqlResult {
        private String sql;
        private List<Object> params;

        /**
         * 获取可执行的SQL（参数已替换）
         */
        public String getExecutableSql() {
            String executableSql = sql;
            for (Object param : params) {
                String paramStr = formatParamValue(param);
                executableSql = executableSql.replaceFirst("\\?", paramStr);
            }
            return executableSql;
        }
    }
}
