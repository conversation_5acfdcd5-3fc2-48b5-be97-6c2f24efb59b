package com.weaver.seconddev.zyhlw.orm.page;

import java.io.Serializable;

/**
 * 分页请求对象，用于统一分页参数
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前页码，从1开始
     */
    private int currentPage;

    /**
     * 每页大小
     */
    private int pageSize;

    /**
     * 默认构造函数
     */
    public PageRequest() {
        this(1, 10);
    }

    /**
     * 构造函数
     *
     * @param currentPage 当前页码，从1开始
     * @param pageSize    每页大小
     */
    public PageRequest(int currentPage, int pageSize) {
        this.currentPage = Math.max(currentPage, 1);
        this.pageSize = Math.max(pageSize, 1);
    }

    /**
     * 创建分页请求对象
     *
     * @param currentPage 当前页码，从1开始
     * @param pageSize    每页大小
     * @return 分页请求对象
     */
    public static PageRequest of(int currentPage, int pageSize) {
        return new PageRequest(currentPage, pageSize);
    }

    /**
     * 获取偏移量（用于SQL LIMIT）
     *
     * @return 偏移量
     */
    public int getOffset() {
        return (currentPage - 1) * pageSize;
    }

    /**
     * 获取下一页的分页请求
     *
     * @return 下一页的分页请求
     */
    public PageRequest next() {
        return new PageRequest(currentPage + 1, pageSize);
    }

    /**
     * 获取上一页的分页请求
     *
     * @return 上一页的分页请求
     */
    public PageRequest previous() {
        return new PageRequest(Math.max(currentPage - 1, 1), pageSize);
    }

    /**
     * 是否有上一页
     *
     * @return 是否有上一页
     */
    public boolean hasPrevious() {
        return currentPage > 1;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = Math.max(currentPage, 1);
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = Math.max(pageSize, 1);
    }

    @Override
    public String toString() {
        return "PageRequest{" +
                "currentPage=" + currentPage +
                ", pageSize=" + pageSize +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PageRequest that = (PageRequest) o;

        if (currentPage != that.currentPage) return false;
        return pageSize == that.pageSize;
    }

    @Override
    public int hashCode() {
        int result = currentPage;
        result = 31 * result + pageSize;
        return result;
    }
}
