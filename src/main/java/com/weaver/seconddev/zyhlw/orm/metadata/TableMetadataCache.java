package com.weaver.seconddev.zyhlw.orm.metadata;

import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 表元数据缓存类，提供高性能的元数据缓存
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class TableMetadataCache {

    private static final Logger logger = LoggerFactory.getLogger(TableMetadataCache.class);

    /**
     * 缓存存储：实体类 -> 表元数据
     */
    private static final ConcurrentHashMap<Class<?>, TableMetadata> ENTITY_CACHE = new ConcurrentHashMap<>();

    /**
     * 缓存存储：表名 -> 表元数据
     */
    private static final ConcurrentHashMap<String, TableMetadata> TABLE_NAME_CACHE = new ConcurrentHashMap<>();

    /**
     * 读写锁，保证缓存操作的线程安全
     */
    private static final ReentrantReadWriteLock LOCK = new ReentrantReadWriteLock();
    private static final ReentrantReadWriteLock.ReadLock READ_LOCK = LOCK.readLock();
    private static final ReentrantReadWriteLock.WriteLock WRITE_LOCK = LOCK.writeLock();

    /**
     * 缓存统计信息
     */
    private static volatile long hitCount = 0;
    private static volatile long missCount = 0;
    private static volatile long totalCount = 0;

    /**
     * 根据实体类获取表元数据
     *
     * @param entityClass 实体类
     * @return 表元数据，如果不存在返回null
     */
    public static TableMetadata getByEntityClass(Class<?> entityClass) {
        if (entityClass == null) {
            return null;
        }

        READ_LOCK.lock();
        try {
            totalCount++;
            TableMetadata metadata = ENTITY_CACHE.get(entityClass);
            if (metadata != null) {
                hitCount++;
                logger.debug("{} 缓存命中，实体类: {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName());
            } else {
                missCount++;
                logger.debug("{} 缓存未命中，实体类: {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName());
            }
            return metadata;
        } finally {
            READ_LOCK.unlock();
        }
    }

    /**
     * 根据表名获取表元数据
     *
     * @param tableName 表名
     * @return 表元数据，如果不存在返回null
     */
    public static TableMetadata getByTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return null;
        }

        READ_LOCK.lock();
        try {
            totalCount++;
            TableMetadata metadata = TABLE_NAME_CACHE.get(tableName);
            if (metadata != null) {
                hitCount++;
                logger.debug("{} 缓存命中，表名: {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, tableName);
            } else {
                missCount++;
                logger.debug("{} 缓存未命中，表名: {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, tableName);
            }
            return metadata;
        } finally {
            READ_LOCK.unlock();
        }
    }

    /**
     * 缓存表元数据
     *
     * @param metadata 表元数据
     */
    public static void put(TableMetadata metadata) {
        if (metadata == null || metadata.getEntityClass() == null || metadata.getTableName() == null) {
            logger.warn("{} 无效的表元数据，跳过缓存", OrmConstants.Log.CACHE_OPERATION_PREFIX);
            return;
        }

        WRITE_LOCK.lock();
        try {
            ENTITY_CACHE.put(metadata.getEntityClass(), metadata);
            TABLE_NAME_CACHE.put(metadata.getTableName(), metadata);
            logger.debug("{} 缓存表元数据，实体类: {}, 表名: {}",
                    OrmConstants.Log.CACHE_OPERATION_PREFIX,
                    metadata.getEntityClass().getName(),
                    metadata.getTableName());
        } finally {
            WRITE_LOCK.unlock();
        }
    }

    /**
     * 移除指定实体类的缓存
     *
     * @param entityClass 实体类
     */
    public static void removeByEntityClass(Class<?> entityClass) {
        if (entityClass == null) {
            return;
        }

        WRITE_LOCK.lock();
        try {
            TableMetadata metadata = ENTITY_CACHE.remove(entityClass);
            if (metadata != null && metadata.getTableName() != null) {
                TABLE_NAME_CACHE.remove(metadata.getTableName());
                logger.debug("{} 移除缓存，实体类: {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, entityClass.getName());
            }
        } finally {
            WRITE_LOCK.unlock();
        }
    }

    /**
     * 移除指定表名的缓存
     *
     * @param tableName 表名
     */
    public static void removeByTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return;
        }

        WRITE_LOCK.lock();
        try {
            TableMetadata metadata = TABLE_NAME_CACHE.remove(tableName);
            if (metadata != null && metadata.getEntityClass() != null) {
                ENTITY_CACHE.remove(metadata.getEntityClass());
                logger.debug("{} 移除缓存，表名: {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, tableName);
            }
        } finally {
            WRITE_LOCK.unlock();
        }
    }

    /**
     * 清空所有缓存
     */
    public static void clear() {
        WRITE_LOCK.lock();
        try {
            ENTITY_CACHE.clear();
            TABLE_NAME_CACHE.clear();
            hitCount = 0;
            missCount = 0;
            totalCount = 0;
            logger.info("{} 清空所有缓存", OrmConstants.Log.CACHE_OPERATION_PREFIX);
        } finally {
            WRITE_LOCK.unlock();
        }
    }

    /**
     * 获取缓存大小
     *
     * @return 缓存大小
     */
    public static int size() {
        read.lock();
        try {
            return ENTITY_CACHE.size();
        } finally {
            read.unlock();
        }
    }

    /**
     * 检查缓存是否为空
     *
     * @return 是否为空
     */
    public static boolean isEmpty() {
        read.lock();
        try {
            return ENTITY_CACHE.isEmpty();
        } finally {
            read.unlock();
        }
    }

    /**
     * 获取缓存命中率
     *
     * @return 缓存命中率（0.0 - 1.0）
     */
    public static double getHitRate() {
        read.lock();
        try {
            return totalCount == 0 ? 0.0 : (double) hitCount / totalCount;
        } finally {
            read.unlock();
        }
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public static String getCacheStats() {
        read.lock();
        try {
            return String.format("缓存统计 - 总访问: %d, 命中: %d, 未命中: %d, 命中率: %.2f%%, 缓存大小: %d",
                    totalCount, hitCount, missCount, getHitRate() * 100, size());
        } finally {
            read.unlock();
        }
    }

    /**
     * 打印缓存统计信息
     */
    public static void printCacheStats() {
        logger.info("{} {}", OrmConstants.Log.CACHE_OPERATION_PREFIX, getCacheStats());
    }
}
