package com.weaver.seconddev.zyhlw.orm.annotation;

import com.weaver.seconddev.zyhlw.orm.config.RepositoryRegistrar;
import org.springframework.context.annotation.Import;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 启用ORM Repository扫描注解
 * 在配置类上使用此注解可以自动扫描和注册@Repository注解的接口
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(RepositoryRegistrar.class)
public @interface EnableOrmRepositories {

    /**
     * 扫描的基础包路径
     * 默认为使用此注解的类所在的包
     */
    String[] basePackages() default {};

    /**
     * 扫描的基础包类
     * 将扫描这些类所在的包
     */
    Class<?>[] basePackageClasses() default {};

    /**
     * Repository代理Bean名称
     * 默认为"repositoryProxy"
     */
    String repositoryProxyBeanName() default "repositoryProxy";

    /**
     * 是否启用默认过滤器
     * 默认启用，只扫描@Repository注解的接口
     */
    boolean useDefaultFilters() default true;

    /**
     * 包含的过滤器
     */
    ComponentScan.Filter[] includeFilters() default {};

    /**
     * 排除的过滤器
     */
    ComponentScan.Filter[] excludeFilters() default {};

    /**
     * 过滤器定义
     */
    @interface ComponentScan {
        @interface Filter {
            /**
             * 过滤器类型
             */
            FilterType type() default FilterType.ANNOTATION;

            /**
             * 过滤器值
             */
            Class<?>[] value() default {};

            /**
             * 过滤器类
             */
            Class<?>[] classes() default {};

            /**
             * 过滤器模式
             */
            String[] pattern() default {};
        }
    }

    /**
     * 过滤器类型枚举
     */
    enum FilterType {
        ANNOTATION,
        ASSIGNABLE_TYPE,
        ASPECTJ,
        REGEX,
        CUSTOM
    }
}
