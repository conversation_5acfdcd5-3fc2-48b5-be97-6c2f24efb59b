package com.weaver.seconddev.zyhlw.orm.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.weaver.seconddev.zyhlw.util.page.PageInfo;

import java.io.Serializable;
import java.util.List;

/**
 * 基础Repository接口，提供统一的CRUD操作
 * 类似Mybatis Plus的BaseMapper，支持泛型类型安全
 *
 * @param <T> 实体类型
 * <AUTHOR>
 * @date 2025/6/27
 */
public interface BaseRepository<T> {

    /**
     * 根据QueryWrapper查询实体列表
     *
     * @param queryWrapper 查询条件构造器
     * @return 实体列表
     */
    List<T> selectList(QueryWrapper<T> queryWrapper);

    /**
     * 根据QueryWrapper查询单个实体
     *
     * @param queryWrapper 查询条件构造器
     * @return 单个实体，如果查询结果为空则返回null
     */
    T selectOne(QueryWrapper<T> queryWrapper);

    /**
     * 根据主键ID查询实体
     *
     * @param id 主键ID
     * @return 实体对象，如果不存在则返回null
     */
    T selectById(Serializable id);

    /**
     * 根据多个主键ID查询实体列表
     *
     * @param idList 主键ID列表
     * @return 实体列表
     */
    List<T> selectBatchIds(List<? extends Serializable> idList);

    /**
     * 分页查询
     *
     * @param currentPage  当前页码，从1开始
     * @param pageSize     每页大小
     * @param queryWrapper 查询条件构造器
     * @return 分页结果
     */
    PageInfo<T> selectPage(int currentPage, int pageSize, QueryWrapper<T> queryWrapper);

    /**
     * 查询总记录数
     *
     * @param queryWrapper 查询条件构造器
     * @return 总记录数
     */
    long selectCount(QueryWrapper<T> queryWrapper);

    /**
     * 插入实体
     *
     * @param entity 实体对象
     * @return 影响行数
     */
    int insert(T entity);

    /**
     * 批量插入实体
     *
     * @param entityList 实体列表
     * @return 影响行数
     */
    int insertBatch(List<T> entityList);

    /**
     * 根据主键ID更新实体
     *
     * @param entity 实体对象，必须包含主键ID
     * @return 影响行数
     */
    int updateById(T entity);

    /**
     * 根据QueryWrapper更新实体
     *
     * @param entity       实体对象
     * @param queryWrapper 更新条件构造器
     * @return 影响行数
     */
    int update(T entity, QueryWrapper<T> queryWrapper);

    /**
     * 批量更新实体
     *
     * @param entityList 实体列表，每个实体必须包含主键ID
     * @return 影响行数
     */
    int updateBatchById(List<T> entityList);

    /**
     * 根据主键ID删除实体
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(Serializable id);

    /**
     * 根据多个主键ID批量删除实体
     *
     * @param idList 主键ID列表
     * @return 影响行数
     */
    int deleteBatchIds(List<? extends Serializable> idList);

    /**
     * 根据QueryWrapper删除实体
     *
     * @param queryWrapper 删除条件构造器
     * @return 影响行数
     */
    int delete(QueryWrapper<T> queryWrapper);

    /**
     * 检查实体是否存在
     *
     * @param queryWrapper 查询条件构造器
     * @return 是否存在
     */
    boolean exists(QueryWrapper<T> queryWrapper);

    /**
     * 根据主键ID检查实体是否存在
     *
     * @param id 主键ID
     * @return 是否存在
     */
    boolean existsById(Serializable id);
}
