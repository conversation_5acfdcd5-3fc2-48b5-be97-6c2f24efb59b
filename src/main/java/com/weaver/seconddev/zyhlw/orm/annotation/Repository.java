package com.weaver.seconddev.zyhlw.orm.annotation;

import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义Repository注解，用于标记Repository接口
 * 被此注解标记的接口将自动创建代理实例并注册为Spring Bean
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface Repository {

    /**
     * Bean名称，默认为接口名首字母小写
     */
    @AliasFor(annotation = Component.class)
    String value() default "";

    /**
     * 是否启用缓存，默认启用
     */
    boolean enableCache() default true;

    /**
     * Repository描述信息
     */
    String description() default "";
}
