package com.weaver.seconddev.zyhlw.orm.constant;

/**
 * ORM相关常量定义
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public interface OrmConstants {

    /**
     * 默认分页大小
     */
    int DEFAULT_PAGE_SIZE = 10;

    /**
     * 最大分页大小
     */
    int MAX_PAGE_SIZE = 1000;

    /**
     * 默认当前页
     */
    int DEFAULT_CURRENT_PAGE = 1;

    /**
     * 主键字段名
     */
    String PRIMARY_KEY_FIELD = "id";

    /**
     * 创建时间字段名
     */
    String CREATE_TIME_FIELD = "createTime";

    /**
     * 更新时间字段名
     */
    String UPDATE_TIME_FIELD = "updateTime";

    /**
     * 创建人字段名
     */
    String CREATOR_FIELD = "creator";

    /**
     * 更新人字段名
     */
    String UPDATER_FIELD = "updater";

    /**
     * 删除标记字段名
     */
    String DELETE_TYPE_FIELD = "deleteType";

    /**
     * 租户字段名
     */
    String TENANT_KEY_FIELD = "tenantKey";

    /**
     * 正常状态（未删除）
     */
    int DELETE_TYPE_NORMAL = 0;

    /**
     * 删除状态
     */
    int DELETE_TYPE_DELETED = 1;

    /**
     * 批量操作默认大小
     */
    int DEFAULT_BATCH_SIZE = 100;

    /**
     * SQL参数占位符
     */
    String SQL_PARAM_PLACEHOLDER = "?";

    /**
     * 数据库字段名分隔符（下划线）
     */
    String DB_FIELD_SEPARATOR = "_";

    /**
     * 实体字段名分隔符（驼峰）
     */
    String ENTITY_FIELD_SEPARATOR = "";

    /**
     * 缓存相关常量
     */
    interface Cache {
        /**
         * 实体元数据缓存前缀
         */
        String ENTITY_METADATA_PREFIX = "orm:entity:metadata:";

        /**
         * 表元数据缓存前缀
         */
        String TABLE_METADATA_PREFIX = "orm:table:metadata:";

        /**
         * Repository实例缓存前缀
         */
        String REPOSITORY_INSTANCE_PREFIX = "orm:repository:instance:";

        /**
         * 默认缓存过期时间（秒）
         */
        long DEFAULT_CACHE_EXPIRE = 3600;
    }

    /**
     * SQL相关常量
     */
    interface Sql {
        /**
         * SELECT语句前缀
         */
        String SELECT_PREFIX = "SELECT";

        /**
         * FROM语句
         */
        String FROM = "FROM";

        /**
         * WHERE语句
         */
        String WHERE = "WHERE";

        /**
         * ORDER BY语句
         */
        String ORDER_BY = "ORDER BY";

        /**
         * GROUP BY语句
         */
        String GROUP_BY = "GROUP BY";

        /**
         * LIMIT语句
         */
        String LIMIT = "LIMIT";

        /**
         * COUNT函数
         */
        String COUNT_FUNCTION = "COUNT(*)";

        /**
         * INSERT语句前缀
         */
        String INSERT_PREFIX = "INSERT INTO";

        /**
         * UPDATE语句前缀
         */
        String UPDATE_PREFIX = "UPDATE";

        /**
         * DELETE语句前缀
         */
        String DELETE_PREFIX = "DELETE FROM";

        /**
         * VALUES语句
         */
        String VALUES = "VALUES";

        /**
         * SET语句
         */
        String SET = "SET";
    }

    /**
     * 日志相关常量
     */
    interface Log {
        /**
         * SQL执行日志前缀
         */
        String SQL_EXECUTION_PREFIX = "[ORM-SQL]";

        /**
         * 实体映射日志前缀
         */
        String ENTITY_MAPPING_PREFIX = "[ORM-MAPPING]";

        /**
         * 缓存操作日志前缀
         */
        String CACHE_OPERATION_PREFIX = "[ORM-CACHE]";

        /**
         * 性能监控日志前缀
         */
        String PERFORMANCE_PREFIX = "[ORM-PERF]";
    }
}
