package com.weaver.seconddev.zyhlw.orm.mapper;

import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import com.weaver.seconddev.zyhlw.orm.exception.OrmException;
import com.weaver.seconddev.zyhlw.orm.metadata.EntityMetadataManager;
import com.weaver.seconddev.zyhlw.orm.metadata.TableMetadata;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 结果映射器实现类，处理查询结果到实体类的映射
 * 支持类型转换、字段映射、null值处理等功能
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Component
public class ResultMapperImpl implements ResultMapper {

    private static final Logger logger = LoggerFactory.getLogger(ResultMapperImpl.class);

    @Resource
    private EntityMetadataManager entityMetadataManager;

    @Override
    public <T> T mapToEntity(Map<String, Object> resultMap, Class<T> entityClass) {
        if (resultMap == null || resultMap.isEmpty()) {
            return null;
        }
        if (entityClass == null) {
            throw new OrmException.EntityMappingException("实体类不能为null");
        }

        try {
            logger.debug("{} 开始映射结果到实体类: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName());
            
            // 获取实体元数据
            TableMetadata metadata = entityMetadataManager.getTableMetadata(entityClass);
            
            // 创建实体实例
            T entity = entityClass.newInstance();
            
            // 遍历结果Map，设置实体字段值
            for (Map.Entry<String, Object> entry : resultMap.entrySet()) {
                String columnName = entry.getKey();
                Object value = entry.getValue();
                
                // 根据数据库字段名获取实体字段名
                String fieldName = metadata.getFieldName(columnName);
                if (fieldName == null) {
                    // 如果没有找到映射，尝试驼峰转换
                    fieldName = convertFieldName(columnName);
                }
                
                // 检查实体是否有该字段
                if (metadata.hasField(fieldName)) {
                    Field field = metadata.getField(fieldName);
                    Class<?> fieldType = metadata.getFieldType(fieldName);
                    
                    // 类型转换并设置字段值
                    Object convertedValue = convertValue(value, fieldType);
                    field.set(entity, convertedValue);
                    
                    logger.trace("{} 设置字段值: {} = {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, fieldName, convertedValue);
                } else {
                    logger.trace("{} 跳过未知字段: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, columnName);
                }
            }
            
            logger.debug("{} 完成实体映射: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName());
            return entity;
            
        } catch (Exception e) {
            logger.error("{} 映射结果到实体类失败: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName(), e);
            throw new OrmException.EntityMappingException("映射结果到实体类失败: " + e.getMessage(), e);
        }
    }

    @Override
    public <T> List<T> mapToEntityList(List<Map<String, Object>> resultList, Class<T> entityClass) {
        if (resultList == null || resultList.isEmpty()) {
            return new ArrayList<>();
        }
        if (entityClass == null) {
            throw new OrmException.EntityMappingException("实体类不能为null");
        }

        try {
            logger.debug("{} 开始批量映射结果到实体类: {}, 数量: {}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName(), resultList.size());
            
            List<T> entityList = new ArrayList<>();
            for (Map<String, Object> resultMap : resultList) {
                T entity = mapToEntity(resultMap, entityClass);
                if (entity != null) {
                    entityList.add(entity);
                }
            }
            
            logger.debug("{} 完成批量实体映射: {}, 成功数量: {}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName(), entityList.size());
            return entityList;
            
        } catch (Exception e) {
            logger.error("{} 批量映射结果到实体类失败: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName(), e);
            throw new OrmException.EntityMappingException("批量映射结果到实体类失败: " + e.getMessage(), e);
        }
    }

    @Override
    public <T> Map<String, Object> mapToMap(T entity) {
        if (entity == null) {
            return new HashMap<>();
        }

        try {
            Class<?> entityClass = entity.getClass();
            logger.debug("{} 开始映射实体到Map: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName());
            
            // 获取实体元数据
            TableMetadata metadata = entityMetadataManager.getTableMetadata(entityClass);
            
            Map<String, Object> resultMap = new HashMap<>();
            
            // 遍历实体字段，获取字段值
            for (Map.Entry<String, Field> entry : metadata.getFieldMap().entrySet()) {
                String fieldName = entry.getKey();
                Field field = entry.getValue();
                
                try {
                    Object value = field.get(entity);
                    String columnName = metadata.getColumnName(fieldName);
                    
                    if (columnName != null) {
                        resultMap.put(columnName, value);
                    } else {
                        // 如果没有找到映射，使用下划线转换
                        columnName = convertToDbFieldName(fieldName);
                        resultMap.put(columnName, value);
                    }
                    
                    logger.trace("{} 获取字段值: {} = {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, fieldName, value);
                } catch (IllegalAccessException e) {
                    logger.warn("{} 无法访问字段: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, fieldName);
                }
            }
            
            logger.debug("{} 完成实体到Map映射: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName());
            return resultMap;
            
        } catch (Exception e) {
            logger.error("{} 映射实体到Map失败: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, entity.getClass().getName(), e);
            throw new OrmException.EntityMappingException("映射实体到Map失败: " + e.getMessage(), e);
        }
    }

    @Override
    public <T> List<Map<String, Object>> mapToMapList(List<T> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return new ArrayList<>();
        }

        try {
            logger.debug("{} 开始批量映射实体到Map, 数量: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityList.size());
            
            List<Map<String, Object>> resultList = new ArrayList<>();
            for (T entity : entityList) {
                Map<String, Object> resultMap = mapToMap(entity);
                resultList.add(resultMap);
            }
            
            logger.debug("{} 完成批量实体到Map映射, 数量: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, resultList.size());
            return resultList;
            
        } catch (Exception e) {
            logger.error("{} 批量映射实体到Map失败", OrmConstants.Log.ENTITY_MAPPING_PREFIX, e);
            throw new OrmException.EntityMappingException("批量映射实体到Map失败: " + e.getMessage(), e);
        }
    }

    @Override
    public <T> T convertValue(Object value, Class<T> targetType) {
        if (value == null) {
            return getDefaultValue(targetType);
        }
        
        if (targetType.isInstance(value)) {
            return targetType.cast(value);
        }

        try {
            // 字符串类型转换
            if (targetType == String.class) {
                return targetType.cast(value.toString());
            }
            
            // 数值类型转换
            if (targetType == Integer.class || targetType == int.class) {
                if (value instanceof Number) {
                    return targetType.cast(((Number) value).intValue());
                }
                return targetType.cast(Integer.parseInt(value.toString()));
            }
            
            if (targetType == Long.class || targetType == long.class) {
                if (value instanceof Number) {
                    return targetType.cast(((Number) value).longValue());
                }
                return targetType.cast(Long.parseLong(value.toString()));
            }
            
            if (targetType == Float.class || targetType == float.class) {
                if (value instanceof Number) {
                    return targetType.cast(((Number) value).floatValue());
                }
                return targetType.cast(Float.parseFloat(value.toString()));
            }
            
            if (targetType == Double.class || targetType == double.class) {
                if (value instanceof Number) {
                    return targetType.cast(((Number) value).doubleValue());
                }
                return targetType.cast(Double.parseDouble(value.toString()));
            }
            
            if (targetType == BigDecimal.class) {
                if (value instanceof Number) {
                    return targetType.cast(new BigDecimal(value.toString()));
                }
                return targetType.cast(new BigDecimal(value.toString()));
            }
            
            // 布尔类型转换
            if (targetType == Boolean.class || targetType == boolean.class) {
                if (value instanceof Number) {
                    return targetType.cast(((Number) value).intValue() != 0);
                }
                return targetType.cast(Boolean.parseBoolean(value.toString()));
            }
            
            // 日期时间类型转换
            if (targetType == Date.class) {
                if (value instanceof Date) {
                    return targetType.cast(value);
                }
                if (value instanceof LocalDateTime) {
                    return targetType.cast(java.sql.Timestamp.valueOf((LocalDateTime) value));
                }
                // 可以添加更多日期格式解析
            }
            
            if (targetType == LocalDateTime.class) {
                if (value instanceof LocalDateTime) {
                    return targetType.cast(value);
                }
                if (value instanceof Date) {
                    return targetType.cast(((Date) value).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime());
                }
                if (value instanceof String) {
                    return targetType.cast(LocalDateTime.parse(value.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
            }
            
            // 默认使用toString转换
            logger.warn("{} 无法转换类型 {} 到 {}, 使用toString", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, value.getClass().getName(), targetType.getName());
            return targetType.cast(value.toString());
            
        } catch (Exception e) {
            logger.error("{} 类型转换失败: {} -> {}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, value.getClass().getName(), targetType.getName(), e);
            return getDefaultValue(targetType);
        }
    }

    @Override
    public String convertFieldName(String dbFieldName) {
        return entityMetadataManager.underscoreToCamel(dbFieldName);
    }

    @Override
    public String convertToDbFieldName(String entityFieldName) {
        if (entityFieldName == null || entityFieldName.isEmpty()) {
            return entityFieldName;
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < entityFieldName.length(); i++) {
            char c = entityFieldName.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append(OrmConstants.DB_FIELD_SEPARATOR);
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    @Override
    public boolean isEmpty(Object value) {
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return ((String) value).trim().isEmpty();
        }
        if (value instanceof Collection) {
            return ((Collection<?>) value).isEmpty();
        }
        if (value instanceof Map) {
            return ((Map<?, ?>) value).isEmpty();
        }
        if (value.getClass().isArray()) {
            return java.lang.reflect.Array.getLength(value) == 0;
        }
        return false;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getDefaultValue(Class<T> fieldType) {
        if (fieldType == null) {
            return null;
        }
        
        if (fieldType.isPrimitive()) {
            if (fieldType == int.class) {
                return (T) Integer.valueOf(0);
            }
            if (fieldType == long.class) {
                return (T) Long.valueOf(0L);
            }
            if (fieldType == float.class) {
                return (T) Float.valueOf(0.0f);
            }
            if (fieldType == double.class) {
                return (T) Double.valueOf(0.0);
            }
            if (fieldType == boolean.class) {
                return (T) Boolean.FALSE;
            }
            if (fieldType == char.class) {
                return (T) Character.valueOf('\0');
            }
            if (fieldType == byte.class) {
                return (T) Byte.valueOf((byte) 0);
            }
            if (fieldType == short.class) {
                return (T) Short.valueOf((short) 0);
            }
        }
        
        return null;
    }
}
