package com.weaver.seconddev.zyhlw.orm.executor;

import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import com.weaver.seconddev.zyhlw.orm.exception.OrmException;
import com.weaver.seconddev.zyhlw.service.IDataSqlService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * SQL执行器实现类，封装IDataSqlService的调用逻辑
 * 提供统一的SQL执行接口，支持查询、更新、分页等操作
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Component
public class SqlExecutorImpl implements SqlExecutor {

    private static final Logger logger = LoggerFactory.getLogger(SqlExecutorImpl.class);

    @Resource
    private IDataSqlService dataSqlService;

    @Override
    public List<Map<String, Object>> executeQuery(String sql, List<Object> params, SourceType sourceType) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new OrmException.SqlExecutionException("SQL语句不能为空");
        }

        try {
            logger.debug("{} 执行查询SQL: {}, 参数: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, params);
            
            long startTime = System.currentTimeMillis();
            List<Map<String, Object>> result;
            
            if (params == null || params.isEmpty()) {
                result = dataSqlService.eBuilderFromSqlAll(sql, sourceType);
            } else {
                List<String> stringParams = convertParamsToString(params);
                result = dataSqlService.eBuilderFromSqlAll(sql, sourceType, stringParams);
            }
            
            long endTime = System.currentTimeMillis();
            logger.debug("{} 查询完成，耗时: {}ms, 结果数量: {}", 
                    OrmConstants.Log.PERFORMANCE_PREFIX, (endTime - startTime), result.size());
            
            return result;
        } catch (Exception e) {
            logger.error("{} 执行查询SQL失败: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, e);
            throw new OrmException.SqlExecutionException("执行查询SQL失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> executeQueryOne(String sql, List<Object> params, SourceType sourceType) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new OrmException.SqlExecutionException("SQL语句不能为空");
        }

        try {
            logger.debug("{} 执行单条查询SQL: {}, 参数: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, params);
            
            long startTime = System.currentTimeMillis();
            Map<String, Object> result;
            
            if (params == null || params.isEmpty()) {
                result = dataSqlService.eBuilderFromSqlOne(sql, sourceType);
            } else {
                // 对于单条查询，我们先查询全部然后取第一条
                List<String> stringParams = convertParamsToString(params);
                List<Map<String, Object>> resultList = dataSqlService.eBuilderFromSqlAll(sql, sourceType, stringParams);
                result = resultList.isEmpty() ? null : resultList.get(0);
            }
            
            long endTime = System.currentTimeMillis();
            logger.debug("{} 单条查询完成，耗时: {}ms", 
                    OrmConstants.Log.PERFORMANCE_PREFIX, (endTime - startTime));
            
            return result;
        } catch (Exception e) {
            logger.error("{} 执行单条查询SQL失败: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, e);
            throw new OrmException.SqlExecutionException("执行单条查询SQL失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Map<String, Object>> executePageQuery(String sql, List<Object> params, SourceType sourceType,
                                                      int currentPage, int pageSize) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new OrmException.SqlExecutionException("SQL语句不能为空");
        }
        
        if (currentPage < 1) {
            currentPage = 1;
        }
        if (pageSize < 1) {
            pageSize = OrmConstants.DEFAULT_PAGE_SIZE;
        }
        if (pageSize > OrmConstants.MAX_PAGE_SIZE) {
            pageSize = OrmConstants.MAX_PAGE_SIZE;
        }

        try {
            logger.debug("{} 执行分页查询SQL: {}, 参数: {}, 页码: {}, 页大小: {}", 
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, params, currentPage, pageSize);
            
            long startTime = System.currentTimeMillis();
            List<Map<String, Object>> result;
            
            if (params == null || params.isEmpty()) {
                result = dataSqlService.ebuilderFromSql(sql, currentPage, pageSize, sourceType);
            } else {
                List<String> stringParams = convertParamsToString(params);
                result = dataSqlService.ebuilderFromSql(sql, currentPage, pageSize, sourceType, stringParams);
            }
            
            long endTime = System.currentTimeMillis();
            logger.debug("{} 分页查询完成，耗时: {}ms, 结果数量: {}", 
                    OrmConstants.Log.PERFORMANCE_PREFIX, (endTime - startTime), result.size());
            
            return result;
        } catch (Exception e) {
            logger.error("{} 执行分页查询SQL失败: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, e);
            throw new OrmException.SqlExecutionException("执行分页查询SQL失败: " + e.getMessage(), e);
        }
    }

    @Override
    public long executeCount(String sql, List<Object> params, SourceType sourceType) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new OrmException.SqlExecutionException("SQL语句不能为空");
        }

        try {
            logger.debug("{} 执行统计查询SQL: {}, 参数: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, params);
            
            long startTime = System.currentTimeMillis();
            Map<String, Object> result = executeQueryOne(sql, params, sourceType);
            
            long count = 0;
            if (result != null && !result.isEmpty()) {
                Object countValue = result.values().iterator().next();
                if (countValue != null) {
                    count = Long.parseLong(countValue.toString());
                }
            }
            
            long endTime = System.currentTimeMillis();
            logger.debug("{} 统计查询完成，耗时: {}ms, 结果: {}", 
                    OrmConstants.Log.PERFORMANCE_PREFIX, (endTime - startTime), count);
            
            return count;
        } catch (Exception e) {
            logger.error("{} 执行统计查询SQL失败: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, e);
            throw new OrmException.SqlExecutionException("执行统计查询SQL失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int executeUpdate(String sql, List<Object> params, SourceType sourceType) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new OrmException.SqlExecutionException("SQL语句不能为空");
        }

        try {
            logger.debug("{} 执行更新SQL: {}, 参数: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, params);
            
            long startTime = System.currentTimeMillis();
            
            // 使用通用SQL执行方法
            List<String> stringParams = convertParamsToString(params);
            Map<String, Object> result = dataSqlService.executeSqlForWithTrans(sql, sourceType, null, stringParams, 
                    null, false, false, false);
            
            int affectedRows = 0;
            if (result != null && result.containsKey("affectedRows")) {
                affectedRows = Integer.parseInt(result.get("affectedRows").toString());
            }
            
            long endTime = System.currentTimeMillis();
            logger.debug("{} 更新完成，耗时: {}ms, 影响行数: {}", 
                    OrmConstants.Log.PERFORMANCE_PREFIX, (endTime - startTime), affectedRows);
            
            return affectedRows;
        } catch (Exception e) {
            logger.error("{} 执行更新SQL失败: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, e);
            throw new OrmException.SqlExecutionException("执行更新SQL失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int[] executeBatchUpdate(List<String> sqlList, List<List<Object>> paramsList, SourceType sourceType) {
        if (sqlList == null || sqlList.isEmpty()) {
            throw new OrmException.SqlExecutionException("SQL语句列表不能为空");
        }

        try {
            logger.debug("{} 执行批量更新，SQL数量: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sqlList.size());
            
            long startTime = System.currentTimeMillis();
            int[] results = new int[sqlList.size()];
            
            for (int i = 0; i < sqlList.size(); i++) {
                String sql = sqlList.get(i);
                List<Object> params = (paramsList != null && i < paramsList.size()) ? paramsList.get(i) : null;
                results[i] = executeUpdate(sql, params, sourceType);
            }
            
            long endTime = System.currentTimeMillis();
            logger.debug("{} 批量更新完成，耗时: {}ms", 
                    OrmConstants.Log.PERFORMANCE_PREFIX, (endTime - startTime));
            
            return results;
        } catch (Exception e) {
            logger.error("{} 执行批量更新失败", OrmConstants.Log.SQL_EXECUTION_PREFIX, e);
            throw new OrmException.SqlExecutionException("执行批量更新失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> executeUpdateWithTrans(String sql, List<Object> params, SourceType sourceType,
                                                      String transId, Boolean startTrans, Boolean commit, Boolean rollback) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new OrmException.SqlExecutionException("SQL语句不能为空");
        }

        try {
            logger.debug("{} 执行事务更新SQL: {}, 参数: {}, 事务ID: {}", 
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, params, transId);
            
            long startTime = System.currentTimeMillis();
            
            List<String> stringParams = convertParamsToString(params);
            Map<String, Object> result = dataSqlService.executeSqlForWithTrans(sql, sourceType, null, stringParams, 
                    transId, startTrans, commit, rollback);
            
            long endTime = System.currentTimeMillis();
            logger.debug("{} 事务更新完成，耗时: {}ms", 
                    OrmConstants.Log.PERFORMANCE_PREFIX, (endTime - startTime));
            
            return result;
        } catch (Exception e) {
            logger.error("{} 执行事务更新SQL失败: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, e);
            throw new OrmException.SqlExecutionException("执行事务更新SQL失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Map<String, Object>> executeCommonQuery(String sql, List<Object> params, SourceType sourceType, String groupId) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new OrmException.SqlExecutionException("SQL语句不能为空");
        }

        try {
            logger.debug("{} 执行通用查询SQL: {}, 参数: {}, 分组ID: {}", 
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, params, groupId);
            
            long startTime = System.currentTimeMillis();
            
            List<String> stringParams = convertParamsToString(params);
            List<Map<String, Object>> result = dataSqlService.executeCommonSqlAll(sql, sourceType, groupId, stringParams);
            
            long endTime = System.currentTimeMillis();
            logger.debug("{} 通用查询完成，耗时: {}ms, 结果数量: {}", 
                    OrmConstants.Log.PERFORMANCE_PREFIX, (endTime - startTime), result.size());
            
            return result;
        } catch (Exception e) {
            logger.error("{} 执行通用查询SQL失败: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, e);
            throw new OrmException.SqlExecutionException("执行通用查询SQL失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> executeCommonQueryOne(String sql, List<Object> params, SourceType sourceType, String groupId) {
        if (sql == null || sql.trim().isEmpty()) {
            throw new OrmException.SqlExecutionException("SQL语句不能为空");
        }

        try {
            logger.debug("{} 执行通用单条查询SQL: {}, 参数: {}, 分组ID: {}", 
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, params, groupId);
            
            long startTime = System.currentTimeMillis();
            
            List<String> stringParams = convertParamsToString(params);
            Map<String, Object> result = dataSqlService.executeCommonSqlOne(sql, sourceType, groupId, stringParams);
            
            long endTime = System.currentTimeMillis();
            logger.debug("{} 通用单条查询完成，耗时: {}ms", 
                    OrmConstants.Log.PERFORMANCE_PREFIX, (endTime - startTime));
            
            return result;
        } catch (Exception e) {
            logger.error("{} 执行通用单条查询SQL失败: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, sql, e);
            throw new OrmException.SqlExecutionException("执行通用单条查询SQL失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将参数列表转换为字符串列表
     *
     * @param params 参数列表
     * @return 字符串参数列表
     */
    private List<String> convertParamsToString(List<Object> params) {
        if (params == null) {
            return null;
        }
        
        List<String> stringParams = new ArrayList<>();
        for (Object param : params) {
            if (param == null) {
                stringParams.add(null);
            } else {
                stringParams.add(param.toString());
            }
        }
        return stringParams;
    }
}
