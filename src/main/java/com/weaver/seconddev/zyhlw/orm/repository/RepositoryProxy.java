package com.weaver.seconddev.zyhlw.orm.repository;

import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import com.weaver.seconddev.zyhlw.orm.exception.OrmException;
import com.weaver.seconddev.zyhlw.orm.factory.RepositoryFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Proxy;
import java.lang.reflect.Type;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Repository动态代理类，支持接口式的Repository定义
 * 通过动态代理实现自定义Repository接口，委托给BaseRepository处理
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Component
public class RepositoryProxy implements InvocationHandler {

    private static final Logger logger = LoggerFactory.getLogger(RepositoryProxy.class);

    @Resource
    private RepositoryFactory repositoryFactory;

    /**
     * 代理实例缓存：接口类 -> 代理实例
     */
    private static final ConcurrentHashMap<Class<?>, Object> PROXY_CACHE = new ConcurrentHashMap<>();

    /**
     * 实体类缓存：接口类 -> 实体类
     */
    private static final ConcurrentHashMap<Class<?>, Class<?>> ENTITY_CLASS_CACHE = new ConcurrentHashMap<>();

    /**
     * 目标Repository实例
     */
    private BaseRepository<?> targetRepository;

    /**
     * 实体类
     */
    private Class<?> entityClass;

    /**
     * 创建Repository接口的代理实例
     *
     * @param repositoryInterface Repository接口类
     * @param <T>                 Repository接口类型
     * @return 代理实例
     */
    @SuppressWarnings("unchecked")
    public <T> T createProxy(Class<T> repositoryInterface) {
        if (repositoryInterface == null) {
            throw new OrmException.ConfigurationException("Repository接口不能为null");
        }

        if (!repositoryInterface.isInterface()) {
            throw new OrmException.ConfigurationException("必须是接口类型: " + repositoryInterface.getName());
        }

        try {
            logger.debug("{} 创建Repository代理，接口: {}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, repositoryInterface.getName());

            // 先从缓存中获取
            T proxy = (T) PROXY_CACHE.get(repositoryInterface);
            if (proxy != null) {
                logger.debug("{} 代理缓存命中，接口: {}", 
                        OrmConstants.Log.CACHE_OPERATION_PREFIX, repositoryInterface.getName());
                return proxy;
            }

            // 解析实体类型
            Class<?> entityClass = resolveEntityClass(repositoryInterface);
            ENTITY_CLASS_CACHE.put(repositoryInterface, entityClass);

            // 获取BaseRepository实例
            BaseRepository<?> baseRepository = repositoryFactory.getRepository(entityClass);

            // 创建代理实例
            RepositoryProxy handler = new RepositoryProxy();
            handler.targetRepository = baseRepository;
            handler.entityClass = entityClass;

            proxy = (T) Proxy.newProxyInstance(
                    repositoryInterface.getClassLoader(),
                    new Class<?>[]{repositoryInterface},
                    handler
            );

            // 缓存代理实例
            PROXY_CACHE.put(repositoryInterface, proxy);

            logger.debug("{} Repository代理创建完成，接口: {}, 实体类: {}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, repositoryInterface.getName(), entityClass.getName());

            return proxy;

        } catch (Exception e) {
            logger.error("{} 创建Repository代理失败，接口: {}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, repositoryInterface.getName(), e);
            throw new OrmException.ConfigurationException("创建Repository代理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        try {
            logger.trace("{} 代理方法调用: {}.{}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName(), method.getName());

            // 检查是否是BaseRepository的方法
            if (isBaseRepositoryMethod(method)) {
                // 直接委托给BaseRepository
                return method.invoke(targetRepository, args);
            }

            // 检查是否是Object的方法
            if (isObjectMethod(method)) {
                return handleObjectMethod(proxy, method, args);
            }

            // 处理自定义方法
            return handleCustomMethod(method, args);

        } catch (Exception e) {
            logger.error("{} 代理方法调用失败: {}.{}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName(), method.getName(), e);
            throw new OrmException.SqlExecutionException("代理方法调用失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解析Repository接口的实体类型
     *
     * @param repositoryInterface Repository接口
     * @return 实体类
     */
    private Class<?> resolveEntityClass(Class<?> repositoryInterface) {
        // 先从缓存中获取
        Class<?> cachedEntityClass = ENTITY_CLASS_CACHE.get(repositoryInterface);
        if (cachedEntityClass != null) {
            return cachedEntityClass;
        }

        // 检查接口是否继承自BaseRepository
        Type[] genericInterfaces = repositoryInterface.getGenericInterfaces();
        for (Type genericInterface : genericInterfaces) {
            if (genericInterface instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) genericInterface;
                Type rawType = parameterizedType.getRawType();
                
                if (rawType == BaseRepository.class) {
                    Type[] typeArguments = parameterizedType.getActualTypeArguments();
                    if (typeArguments.length > 0 && typeArguments[0] instanceof Class) {
                        return (Class<?>) typeArguments[0];
                    }
                }
            }
        }

        // 检查父接口
        Class<?>[] superInterfaces = repositoryInterface.getInterfaces();
        for (Class<?> superInterface : superInterfaces) {
            try {
                return resolveEntityClass(superInterface);
            } catch (Exception e) {
                // 继续检查下一个接口
            }
        }

        throw new OrmException.ConfigurationException(
                "无法解析Repository接口的实体类型: " + repositoryInterface.getName() + 
                "，请确保接口继承自BaseRepository<T>");
    }

    /**
     * 检查是否是BaseRepository的方法
     *
     * @param method 方法
     * @return 是否是BaseRepository的方法
     */
    private boolean isBaseRepositoryMethod(Method method) {
        try {
            BaseRepository.class.getMethod(method.getName(), method.getParameterTypes());
            return true;
        } catch (NoSuchMethodException e) {
            return false;
        }
    }

    /**
     * 检查是否是Object的方法
     *
     * @param method 方法
     * @return 是否是Object的方法
     */
    private boolean isObjectMethod(Method method) {
        return method.getDeclaringClass() == Object.class;
    }

    /**
     * 处理Object方法
     *
     * @param proxy  代理对象
     * @param method 方法
     * @param args   参数
     * @return 方法返回值
     */
    private Object handleObjectMethod(Object proxy, Method method, Object[] args) {
        String methodName = method.getName();
        
        switch (methodName) {
            case "toString":
                return "RepositoryProxy[" + entityClass.getName() + "]";
            case "hashCode":
                return System.identityHashCode(proxy);
            case "equals":
                return proxy == args[0];
            default:
                throw new OrmException.ConfigurationException("不支持的Object方法: " + methodName);
        }
    }

    /**
     * 处理自定义方法
     *
     * @param method 方法
     * @param args   参数
     * @return 方法返回值
     */
    private Object handleCustomMethod(Method method, Object[] args) {
        // 目前暂不支持自定义方法，可以在后续版本中扩展
        logger.warn("{} 暂不支持自定义Repository方法: {}", 
                OrmConstants.Log.ENTITY_MAPPING_PREFIX, method.getName());
        
        throw new OrmException.ConfigurationException(
                "暂不支持自定义Repository方法: " + method.getName() + 
                "，请使用BaseRepository提供的标准方法");
    }

    /**
     * 清空代理缓存
     */
    public static void clearProxyCache() {
        PROXY_CACHE.clear();
        ENTITY_CLASS_CACHE.clear();
    }

    /**
     * 获取代理缓存大小
     *
     * @return 缓存大小
     */
    public static int getProxyCacheSize() {
        return PROXY_CACHE.size();
    }

    /**
     * 检查接口是否已被代理
     *
     * @param repositoryInterface Repository接口
     * @return 是否已被代理
     */
    public static boolean isProxied(Class<?> repositoryInterface) {
        return PROXY_CACHE.containsKey(repositoryInterface);
    }
}
