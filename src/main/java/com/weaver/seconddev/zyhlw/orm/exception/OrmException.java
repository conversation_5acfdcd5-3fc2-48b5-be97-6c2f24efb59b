package com.weaver.seconddev.zyhlw.orm.exception;

/**
 * ORM异常类，用于统一处理ORM操作中的异常
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class OrmException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误代码
     */
    private String errorCode;

    public OrmException() {
        super();
    }

    public OrmException(String message) {
        super(message);
    }

    public OrmException(String message, Throwable cause) {
        super(message, cause);
    }

    public OrmException(Throwable cause) {
        super(cause);
    }

    public OrmException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    public OrmException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * SQL执行异常
     */
    public static class SqlExecutionException extends OrmException {
        public SqlExecutionException(String message) {
            super("SQL_EXECUTION_ERROR", message);
        }

        public SqlExecutionException(String message, Throwable cause) {
            super("SQL_EXECUTION_ERROR", message, cause);
        }
    }

    /**
     * 实体映射异常
     */
    public static class EntityMappingException extends OrmException {
        public EntityMappingException(String message) {
            super("ENTITY_MAPPING_ERROR", message);
        }

        public EntityMappingException(String message, Throwable cause) {
            super("ENTITY_MAPPING_ERROR", message, cause);
        }
    }

    /**
     * 元数据异常
     */
    public static class MetadataException extends OrmException {
        public MetadataException(String message) {
            super("METADATA_ERROR", message);
        }

        public MetadataException(String message, Throwable cause) {
            super("METADATA_ERROR", message, cause);
        }
    }

    /**
     * 配置异常
     */
    public static class ConfigurationException extends OrmException {
        public ConfigurationException(String message) {
            super("CONFIGURATION_ERROR", message);
        }

        public ConfigurationException(String message, Throwable cause) {
            super("CONFIGURATION_ERROR", message, cause);
        }
    }
}
