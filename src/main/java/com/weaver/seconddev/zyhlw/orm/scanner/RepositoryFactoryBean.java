package com.weaver.seconddev.zyhlw.orm.scanner;

import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import com.weaver.seconddev.zyhlw.orm.exception.OrmException;
import com.weaver.seconddev.zyhlw.orm.repository.RepositoryProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

/**
 * Repository工厂Bean，用于创建Repository代理实例
 * 实现FactoryBean接口，支持Spring容器管理
 *
 * @param <T> Repository接口类型
 * <AUTHOR>
 * @date 2025/6/27
 */
public class RepositoryFactoryBean<T> implements FactoryBean<T>, InitializingBean, ApplicationContextAware {

    private static final Logger logger = LoggerFactory.getLogger(RepositoryFactoryBean.class);

    /**
     * Repository接口类
     */
    private Class<T> repositoryInterface;

    /**
     * Repository代理实例
     */
    private T repositoryProxy;

    /**
     * Spring应用上下文
     */
    private ApplicationContext applicationContext;

    /**
     * Repository代理工厂
     */
    private RepositoryProxy repositoryProxyFactory;

    /**
     * 构造函数
     */
    public RepositoryFactoryBean() {
    }

    /**
     * 构造函数
     *
     * @param repositoryInterface Repository接口类
     */
    public RepositoryFactoryBean(Class<T> repositoryInterface) {
        this.repositoryInterface = repositoryInterface;
    }

    /**
     * 构造函数
     *
     * @param repositoryInterfaceName Repository接口类名
     */
    @SuppressWarnings("unchecked")
    public RepositoryFactoryBean(String repositoryInterfaceName) {
        try {
            this.repositoryInterface = (Class<T>) Class.forName(repositoryInterfaceName);
        } catch (ClassNotFoundException e) {
            throw new OrmException.ConfigurationException("无法加载Repository接口: " + repositoryInterfaceName, e);
        }
    }

    @Override
    public T getObject() throws Exception {
        return repositoryProxy;
    }

    @Override
    public Class<?> getObjectType() {
        return repositoryInterface;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        if (repositoryInterface == null) {
            throw new OrmException.ConfigurationException("Repository接口不能为null");
        }

        if (repositoryProxyFactory == null) {
            repositoryProxyFactory = applicationContext.getBean(RepositoryProxy.class);
        }

        try {
            logger.debug("{} 创建Repository代理实例: {}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, repositoryInterface.getName());
            
            repositoryProxy = repositoryProxyFactory.createProxy(repositoryInterface);
            
            logger.debug("{} Repository代理实例创建成功: {}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, repositoryInterface.getName());
            
        } catch (Exception e) {
            logger.error("{} 创建Repository代理实例失败: {}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, repositoryInterface.getName(), e);
            throw new OrmException.ConfigurationException("创建Repository代理实例失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 设置Repository接口类
     */
    public void setRepositoryInterface(Class<T> repositoryInterface) {
        this.repositoryInterface = repositoryInterface;
    }

    /**
     * 设置Repository代理工厂
     */
    public void setRepositoryProxy(RepositoryProxy repositoryProxyFactory) {
        this.repositoryProxyFactory = repositoryProxyFactory;
    }

    /**
     * 获取Repository接口类
     */
    public Class<T> getRepositoryInterface() {
        return repositoryInterface;
    }
}
