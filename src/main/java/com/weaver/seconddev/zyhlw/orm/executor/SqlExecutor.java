package com.weaver.seconddev.zyhlw.orm.executor;

import com.weaver.ebuilder.datasource.api.enums.SourceType;

import java.util.List;
import java.util.Map;

/**
 * SQL执行器接口，封装底层SQL执行逻辑
 * 统一管理SQL执行，支持查询、更新、事务等操作
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public interface SqlExecutor {

    /**
     * 执行查询SQL，返回多条记录
     *
     * @param sql        SQL语句
     * @param params     参数列表
     * @param sourceType 数据源类型
     * @return 查询结果列表
     */
    List<Map<String, Object>> executeQuery(String sql, List<Object> params, SourceType sourceType);

    /**
     * 执行查询SQL，返回单条记录
     *
     * @param sql        SQL语句
     * @param params     参数列表
     * @param sourceType 数据源类型
     * @return 查询结果，如果没有结果返回null
     */
    Map<String, Object> executeQueryOne(String sql, List<Object> params, SourceType sourceType);

    /**
     * 执行分页查询SQL
     *
     * @param sql         SQL语句
     * @param params      参数列表
     * @param sourceType  数据源类型
     * @param currentPage 当前页码，从1开始
     * @param pageSize    每页大小
     * @return 分页查询结果
     */
    List<Map<String, Object>> executePageQuery(String sql, List<Object> params, SourceType sourceType,
                                               int currentPage, int pageSize);

    /**
     * 执行统计查询SQL，返回总记录数
     *
     * @param sql        统计SQL语句
     * @param params     参数列表
     * @param sourceType 数据源类型
     * @return 总记录数
     */
    long executeCount(String sql, List<Object> params, SourceType sourceType);

    /**
     * 执行更新SQL（INSERT、UPDATE、DELETE）
     *
     * @param sql        SQL语句
     * @param params     参数列表
     * @param sourceType 数据源类型
     * @return 影响行数
     */
    int executeUpdate(String sql, List<Object> params, SourceType sourceType);

    /**
     * 批量执行更新SQL
     *
     * @param sqlList    SQL语句列表
     * @param paramsList 参数列表的列表
     * @param sourceType 数据源类型
     * @return 影响行数数组
     */
    int[] executeBatchUpdate(List<String> sqlList, List<List<Object>> paramsList, SourceType sourceType);

    /**
     * 在事务中执行更新SQL
     *
     * @param sql        SQL语句
     * @param params     参数列表
     * @param sourceType 数据源类型
     * @param transId    事务ID
     * @param startTrans 是否开启事务
     * @param commit     是否提交事务
     * @param rollback   是否回滚事务
     * @return 执行结果
     */
    Map<String, Object> executeUpdateWithTrans(String sql, List<Object> params, SourceType sourceType,
                                               String transId, Boolean startTrans, Boolean commit, Boolean rollback);

    /**
     * 执行通用查询SQL
     *
     * @param sql        SQL语句
     * @param params     参数列表
     * @param sourceType 数据源类型
     * @param groupId    分组ID
     * @return 查询结果列表
     */
    List<Map<String, Object>> executeCommonQuery(String sql, List<Object> params, SourceType sourceType, String groupId);

    /**
     * 执行通用查询SQL，返回单条记录
     *
     * @param sql        SQL语句
     * @param params     参数列表
     * @param sourceType 数据源类型
     * @param groupId    分组ID
     * @return 查询结果，如果没有结果返回null
     */
    Map<String, Object> executeCommonQueryOne(String sql, List<Object> params, SourceType sourceType, String groupId);
}
