package com.weaver.seconddev.zyhlw.orm.example;

import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.orm.repository.BaseRepository;

/**
 * OutlayBudget Repository接口示例
 * 继承BaseRepository获得标准CRUD操作
 * 可以在此基础上扩展自定义方法
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public interface OutlayBudgetRepository extends BaseRepository<OutlayBudgetDO> {
    
    // 继承BaseRepository的所有方法：
    // - selectList(QueryWrapper<OutlayBudgetDO> queryWrapper)
    // - selectOne(QueryWrapper<OutlayBudgetDO> queryWrapper)
    // - selectById(Serializable id)
    // - selectBatchIds(List<? extends Serializable> idList)
    // - selectPage(int currentPage, int pageSize, QueryWrapper<OutlayBudgetDO> queryWrapper)
    // - selectCount(QueryWrapper<OutlayBudgetDO> queryWrapper)
    // - insert(OutlayBudgetDO entity)
    // - insertBatch(List<OutlayBudgetDO> entityList)
    // - updateById(OutlayBudgetDO entity)
    // - update(OutlayBudgetDO entity, QueryWrapper<OutlayBudgetDO> queryWrapper)
    // - updateBatchById(List<OutlayBudgetDO> entityList)
    // - deleteById(Serializable id)
    // - deleteBatchIds(List<? extends Serializable> idList)
    // - delete(QueryWrapper<OutlayBudgetDO> queryWrapper)
    // - exists(QueryWrapper<OutlayBudgetDO> queryWrapper)
    // - existsById(Serializable id)
    
    // 可以在这里添加自定义方法（暂时不支持，后续版本可扩展）
    // List<OutlayBudgetDO> findByBudgetNo(String budgetNo);
    // List<OutlayBudgetDO> findByApplicantAndStatus(Long applicant, Integer status);
}
