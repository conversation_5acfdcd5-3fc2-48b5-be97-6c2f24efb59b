package com.weaver.seconddev.zyhlw.orm.config;

import com.weaver.seconddev.zyhlw.orm.annotation.EnableOrmRepositories;
import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import com.weaver.seconddev.zyhlw.orm.scanner.RepositoryScanner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Repository注册器，处理@EnableOrmRepositories注解
 * 自动扫描和注册@Repository注解的接口
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class RepositoryRegistrar implements ImportBeanDefinitionRegistrar {

    private static final Logger logger = LoggerFactory.getLogger(RepositoryRegistrar.class);

    @Override
    public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
        AnnotationAttributes attributes = AnnotationAttributes.fromMap(
                importingClassMetadata.getAnnotationAttributes(EnableOrmRepositories.class.getName()));

        if (attributes == null) {
            logger.warn("{} 未找到@EnableOrmRepositories注解", OrmConstants.Log.ENTITY_MAPPING_PREFIX);
            return;
        }

        try {
            logger.info("{} 开始扫描和注册Repository接口", OrmConstants.Log.ENTITY_MAPPING_PREFIX);

            // 获取扫描包路径
            List<String> basePackages = getBasePackages(importingClassMetadata, attributes);

            // 创建Repository扫描器
            RepositoryScanner scanner = new RepositoryScanner(registry);

            // 设置Repository代理Bean名称
            String repositoryProxyBeanName = attributes.getString("repositoryProxyBeanName");
            if (StringUtils.hasText(repositoryProxyBeanName)) {
                scanner.setRepositoryProxyBeanName(repositoryProxyBeanName);
            }

            // 执行扫描
            int scannedCount = 0;
            for (String basePackage : basePackages) {
                logger.debug("{} 扫描包: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, basePackage);
                scannedCount += scanner.scan(basePackage);
            }

            logger.info("{} Repository接口扫描完成，共注册 {} 个Repository", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, scannedCount);

        } catch (Exception e) {
            logger.error("{} Repository接口扫描失败", OrmConstants.Log.ENTITY_MAPPING_PREFIX, e);
            throw new RuntimeException("Repository接口扫描失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取扫描的基础包路径
     */
    private List<String> getBasePackages(AnnotationMetadata importingClassMetadata, AnnotationAttributes attributes) {
        List<String> basePackages = new ArrayList<>();

        // 从basePackages属性获取
        String[] basePackageArray = attributes.getStringArray("basePackages");
        if (basePackageArray.length > 0) {
            basePackages.addAll(Arrays.asList(basePackageArray));
        }

        // 从basePackageClasses属性获取
        Class<?>[] basePackageClasses = attributes.getClassArray("basePackageClasses");
        for (Class<?> clazz : basePackageClasses) {
            basePackages.add(ClassUtils.getPackageName(clazz));
        }

        // 如果没有指定包路径，使用注解所在类的包
        if (basePackages.isEmpty()) {
            String defaultPackage = ClassUtils.getPackageName(importingClassMetadata.getClassName());
            basePackages.add(defaultPackage);
        }

        return basePackages;
    }
}
