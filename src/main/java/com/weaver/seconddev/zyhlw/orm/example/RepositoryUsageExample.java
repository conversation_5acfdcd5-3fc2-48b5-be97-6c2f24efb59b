package com.weaver.seconddev.zyhlw.orm.example;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.orm.factory.RepositoryFactory;
import com.weaver.seconddev.zyhlw.orm.repository.BaseRepository;
import com.weaver.seconddev.zyhlw.util.page.PageInfo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * Repository使用示例
 * 展示如何使用自定义@Repository注解和工厂模式
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Service
public class RepositoryUsageExample {

    /**
     * 方式1：通过@Repository注解自动注入
     * 推荐使用这种方式，简洁且类型安全
     */
    @Resource
    private OutlayBudgetRepository outlayBudgetRepository;

    /**
     * 方式2：通过RepositoryFactory工厂获取
     * 适用于动态获取Repository的场景
     */
    @Resource
    private RepositoryFactory repositoryFactory;

    /**
     * 示例1：使用@Repository注解注入的Repository
     */
    public void exampleWithAnnotationRepository() {
        // 查询所有记录
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        List<OutlayBudgetDO> allBudgets = outlayBudgetRepository.selectList(queryWrapper);
        
        // 根据ID查询
        OutlayBudgetDO budget = outlayBudgetRepository.selectById(1L);
        
        // 条件查询
        QueryWrapper<OutlayBudgetDO> conditionWrapper = new QueryWrapper<>();
        conditionWrapper.eq("applicant", 1001L)
                       .gt("yu_szje", 1000.0f)
                       .orderByDesc("create_time");
        List<OutlayBudgetDO> filteredBudgets = outlayBudgetRepository.selectList(conditionWrapper);
        
        // 分页查询
        PageInfo<OutlayBudgetDO> pageResult = outlayBudgetRepository.selectPage(1, 10, conditionWrapper);
        
        // 统计查询
        long count = outlayBudgetRepository.selectCount(conditionWrapper);
        
        // 插入记录
        OutlayBudgetDO newBudget = new OutlayBudgetDO();
        // 设置字段值...
        int insertResult = outlayBudgetRepository.insert(newBudget);
        
        // 更新记录
        newBudget.setId(1L);
        // 修改字段值...
        int updateResult = outlayBudgetRepository.updateById(newBudget);
        
        // 删除记录
        int deleteResult = outlayBudgetRepository.deleteById(1L);
        
        // 检查存在性
        boolean exists = outlayBudgetRepository.existsById(1L);
    }

    /**
     * 示例2：使用RepositoryFactory工厂获取Repository
     */
    public void exampleWithFactoryRepository() {
        // 通过工厂获取Repository实例
        BaseRepository<OutlayBudgetDO> repository = repositoryFactory.getRepository(OutlayBudgetDO.class);
        
        // 使用方式与注解注入的Repository完全相同
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1);
        List<OutlayBudgetDO> budgets = repository.selectList(queryWrapper);
        
        // 其他操作...
    }

    /**
     * 示例3：使用泛型类型引用
     */
    public void exampleWithTypeReference() {
        // 通过泛型类型引用获取Repository实例
        BaseRepository<OutlayBudgetDO> repository = repositoryFactory.getRepository(
            new RepositoryFactory.TypeReference<OutlayBudgetDO>() {}
        );
        
        // 使用Repository进行操作
        OutlayBudgetDO budget = repository.selectById(1L);
        
        // 其他操作...
    }

    /**
     * 示例4：批量操作
     */
    public void exampleBatchOperations() {
        // 批量插入
        List<OutlayBudgetDO> budgetList = createBudgetList();
        int batchInsertResult = outlayBudgetRepository.insertBatch(budgetList);
        
        // 批量更新
        // 修改budgetList中的数据...
        int batchUpdateResult = outlayBudgetRepository.updateBatchById(budgetList);
        
        // 批量删除
        List<Long> idList = Arrays.asList(1L, 2L, 3L);
        int batchDeleteResult = outlayBudgetRepository.deleteBatchIds(idList);
    }

    /**
     * 示例5：复杂查询条件
     */
    public void exampleComplexQuery() {
        QueryWrapper<OutlayBudgetDO> queryWrapper = new QueryWrapper<>();
        queryWrapper
            .eq("delete_type", 0)  // 未删除
            .in("applicant", Arrays.asList(1001L, 1002L, 1003L))  // 申请人在指定范围内
            .between("create_time", "2025-01-01", "2025-12-31")  // 创建时间在指定范围内
            .like("budget_no", "BUD")  // 预算编号包含BUD
            .gt("yu_szje", 0)  // 余额大于0
            .orderByDesc("create_time")  // 按创建时间降序
            .orderByAsc("budget_no");  // 按预算编号升序
        
        // 分页查询
        PageInfo<OutlayBudgetDO> pageResult = outlayBudgetRepository.selectPage(1, 20, queryWrapper);
        
        System.out.println("总页数: " + pageResult.getTotalPages());
        System.out.println("当前页数据: " + pageResult.getData().size());
    }

    /**
     * 创建测试数据
     */
    private List<OutlayBudgetDO> createBudgetList() {
        // 创建测试数据的逻辑
        return Arrays.asList();
    }
}
