package com.weaver.seconddev.zyhlw.orm.config;

import com.weaver.seconddev.zyhlw.ebuilder.entity.OutlayBudgetDO;
import com.weaver.seconddev.zyhlw.orm.example.OutlayBudgetRepository;
import com.weaver.seconddev.zyhlw.orm.factory.RepositoryFactory;
import com.weaver.seconddev.zyhlw.orm.repository.BaseRepository;
import com.weaver.seconddev.zyhlw.orm.repository.RepositoryProxy;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * Repository配置类，用于管理Repository的创建和配置
 * 提供Repository Bean的定义和自动装配支持
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Configuration
public class RepositoryConfiguration {

    @Resource
    private RepositoryFactory repositoryFactory;

    @Resource
    private RepositoryProxy repositoryProxy;

    /**
     * 创建OutlayBudgetRepository Bean
     * 使用动态代理方式创建
     *
     * @return OutlayBudgetRepository实例
     */
    @Bean
    public OutlayBudgetRepository outlayBudgetRepository() {
        return repositoryProxy.createProxy(OutlayBudgetRepository.class);
    }

    /**
     * 创建OutlayBudgetDO的BaseRepository Bean
     * 使用工厂方式创建
     *
     * @return BaseRepository实例
     */
    @Bean("outlayBudgetBaseRepository")
    public BaseRepository<OutlayBudgetDO> outlayBudgetBaseRepository() {
        return repositoryFactory.getRepository(OutlayBudgetDO.class);
    }

    // 可以在这里添加更多Repository Bean的定义
    // 例如：
    // @Bean
    // public UserRepository userRepository() {
    //     return repositoryProxy.createProxy(UserRepository.class);
    // }
    //
    // @Bean
    // public BaseRepository<UserDO> userBaseRepository() {
    //     return repositoryFactory.getRepository(UserDO.class);
    // }
}
