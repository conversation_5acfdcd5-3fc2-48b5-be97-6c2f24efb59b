package com.weaver.seconddev.zyhlw.orm.config;

import com.weaver.seconddev.zyhlw.orm.annotation.EnableOrmRepositories;
import org.springframework.context.annotation.Configuration;

/**
 * Repository配置类，用于管理Repository的创建和配置
 * 使用@EnableOrmRepositories注解自动扫描和注册@Repository注解的接口
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Configuration
@EnableOrmRepositories(
        basePackages = {
                "com.weaver.seconddev.zyhlw.orm.example",
                "com.weaver.seconddev.zyhlw.**.repository"
        },
        repositoryProxyBeanName = "repositoryProxy"
)
public class RepositoryConfiguration {

    // 不再需要手动定义Repository Bean
    // @Repository注解的接口将自动注册为Spring Bean

    // 使用示例：
    // 1. 在Repository接口上添加@Repository注解
    // 2. 在需要使用的地方通过@Resource或@Autowired注入
    //
    // @Resource
    // private OutlayBudgetRepository outlayBudgetRepository;
    //
    // 或者
    //
    // @Autowired
    // private OutlayBudgetRepository outlayBudgetRepository;
}
