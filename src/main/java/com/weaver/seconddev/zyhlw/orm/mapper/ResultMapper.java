package com.weaver.seconddev.zyhlw.orm.mapper;

import java.util.List;
import java.util.Map;

/**
 * 结果映射器接口，处理查询结果到实体类的映射
 * 支持类型转换、字段映射、null值处理等功能
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public interface ResultMapper {

    /**
     * 将Map结果映射为实体对象
     *
     * @param resultMap   查询结果Map
     * @param entityClass 目标实体类
     * @param <T>         实体类型
     * @return 实体对象，如果resultMap为null或空则返回null
     */
    <T> T mapToEntity(Map<String, Object> resultMap, Class<T> entityClass);

    /**
     * 将Map结果列表映射为实体对象列表
     *
     * @param resultList  查询结果Map列表
     * @param entityClass 目标实体类
     * @param <T>         实体类型
     * @return 实体对象列表
     */
    <T> List<T> mapToEntityList(List<Map<String, Object>> resultList, Class<T> entityClass);

    /**
     * 将实体对象映射为Map
     *
     * @param entity 实体对象
     * @param <T>    实体类型
     * @return Map结果，如果entity为null则返回空Map
     */
    <T> Map<String, Object> mapToMap(T entity);

    /**
     * 将实体对象列表映射为Map列表
     *
     * @param entityList 实体对象列表
     * @param <T>        实体类型
     * @return Map结果列表
     */
    <T> List<Map<String, Object>> mapToMapList(List<T> entityList);

    /**
     * 字段类型转换
     *
     * @param value      原始值
     * @param targetType 目标类型
     * @param <T>        目标类型
     * @return 转换后的值
     */
    <T> T convertValue(Object value, Class<T> targetType);

    /**
     * 数据库字段名转换为实体字段名
     * 支持下划线转驼峰命名
     *
     * @param dbFieldName 数据库字段名
     * @return 实体字段名
     */
    String convertFieldName(String dbFieldName);

    /**
     * 实体字段名转换为数据库字段名
     * 支持驼峰转下划线命名
     *
     * @param entityFieldName 实体字段名
     * @return 数据库字段名
     */
    String convertToDbFieldName(String entityFieldName);

    /**
     * 检查值是否为空
     * 包括null、空字符串、空集合等情况
     *
     * @param value 待检查的值
     * @return 是否为空
     */
    boolean isEmpty(Object value);

    /**
     * 获取默认值
     * 根据字段类型返回相应的默认值
     *
     * @param fieldType 字段类型
     * @param <T>       字段类型
     * @return 默认值
     */
    <T> T getDefaultValue(Class<T> fieldType);
}
