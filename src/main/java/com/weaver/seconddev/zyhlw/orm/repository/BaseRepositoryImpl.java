package com.weaver.seconddev.zyhlw.orm.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.weaver.ebuilder.datasource.api.enums.SourceType;
import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import com.weaver.seconddev.zyhlw.orm.exception.OrmException;
import com.weaver.seconddev.zyhlw.orm.executor.SqlExecutor;
import com.weaver.seconddev.zyhlw.orm.mapper.ResultMapper;
import com.weaver.seconddev.zyhlw.orm.metadata.EntityMetadataManager;
import com.weaver.seconddev.zyhlw.orm.metadata.TableMetadata;
import com.weaver.seconddev.zyhlw.util.QueryWrapperSqlUtils;
import com.weaver.seconddev.zyhlw.util.page.PageInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * BaseRepository通用实现类，提供标准CRUD操作的具体实现
 * 集成SqlExecutor执行SQL，使用ResultMapper进行结果映射，使用EntityMetadataManager获取元数据信息
 *
 * @param <T> 实体类型
 * <AUTHOR>
 * @date 2025/6/27
 */
@Component
public class BaseRepositoryImpl<T> implements BaseRepository<T> {

    private static final Logger logger = LoggerFactory.getLogger(BaseRepositoryImpl.class);

    @Resource
    private SqlExecutor sqlExecutor;

    @Resource
    private ResultMapper resultMapper;

    @Resource
    private EntityMetadataManager entityMetadataManager;

    /**
     * 实体类型，通过泛型推断获取
     */
    private Class<T> entityClass;

    /**
     * 表元数据
     */
    private TableMetadata tableMetadata;

    /**
     * 初始化实体类型和表元数据
     */
    @SuppressWarnings("unchecked")
    private void initEntityClass() {
        if (entityClass == null) {
            Type superClass = getClass().getGenericSuperclass();
            if (superClass instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) superClass;
                Type[] typeArguments = parameterizedType.getActualTypeArguments();
                if (typeArguments.length > 0) {
                    entityClass = (Class<T>) typeArguments[0];
                }
            }
            
            if (entityClass == null) {
                throw new OrmException.ConfigurationException("无法推断实体类型，请确保正确使用泛型");
            }
            
            // 获取表元数据
            tableMetadata = entityMetadataManager.getTableMetadata(entityClass);
            
            logger.debug("{} 初始化Repository，实体类: {}, 表名: {}", 
                    OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName(), tableMetadata.getTableName());
        }
    }

    /**
     * 设置实体类型（用于工厂创建时）
     */
    public void setEntityClass(Class<T> entityClass) {
        this.entityClass = entityClass;
        this.tableMetadata = entityMetadataManager.getTableMetadata(entityClass);
    }

    @Override
    public List<T> selectList(QueryWrapper<T> queryWrapper) {
        initEntityClass();
        
        if (queryWrapper == null) {
            queryWrapper = new QueryWrapper<>();
        }
        
        try {
            logger.debug("{} 执行selectList查询，实体类: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName());
            
            // 设置实体类到QueryWrapper
            queryWrapper.setEntityClass(entityClass);
            
            // 提取SQL和参数
            QueryWrapperSqlUtils.CompleteSqlResult sqlResult = QueryWrapperSqlUtils.extractCompleteSql(queryWrapper);
            
            // 构建完整的SELECT SQL
            String selectSql = buildSelectSql(sqlResult.getSql(), sqlResult.getTableName());
            
            // 执行查询
            List<Map<String, Object>> resultList = sqlExecutor.executeQuery(selectSql, sqlResult.getParams(), SourceType.EXTERNAL);
            
            // 映射结果
            return resultMapper.mapToEntityList(resultList, entityClass);
            
        } catch (Exception e) {
            logger.error("{} selectList查询失败，实体类: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("selectList查询失败: " + e.getMessage(), e);
        }
    }

    @Override
    public T selectOne(QueryWrapper<T> queryWrapper) {
        initEntityClass();
        
        if (queryWrapper == null) {
            queryWrapper = new QueryWrapper<>();
        }
        
        try {
            logger.debug("{} 执行selectOne查询，实体类: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName());
            
            // 设置实体类到QueryWrapper
            queryWrapper.setEntityClass(entityClass);
            
            // 提取SQL和参数
            QueryWrapperSqlUtils.CompleteSqlResult sqlResult = QueryWrapperSqlUtils.extractCompleteSql(queryWrapper);
            
            // 构建完整的SELECT SQL
            String selectSql = buildSelectSql(sqlResult.getSql(), sqlResult.getTableName());
            
            // 执行查询
            Map<String, Object> result = sqlExecutor.executeQueryOne(selectSql, sqlResult.getParams(), SourceType.EXTERNAL);
            
            // 映射结果
            return resultMapper.mapToEntity(result, entityClass);
            
        } catch (Exception e) {
            logger.error("{} selectOne查询失败，实体类: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("selectOne查询失败: " + e.getMessage(), e);
        }
    }

    @Override
    public T selectById(Serializable id) {
        initEntityClass();
        
        if (id == null) {
            return null;
        }
        
        try {
            logger.debug("{} 执行selectById查询，实体类: {}, ID: {}", 
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), id);
            
            // 构建按ID查询的QueryWrapper
            QueryWrapper<T> queryWrapper = new QueryWrapper<>();
            queryWrapper.setEntityClass(entityClass);
            queryWrapper.eq(tableMetadata.getPrimaryKeyField(), id);
            
            return selectOne(queryWrapper);
            
        } catch (Exception e) {
            logger.error("{} selectById查询失败，实体类: {}, ID: {}", 
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), id, e);
            throw new OrmException.SqlExecutionException("selectById查询失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<T> selectBatchIds(List<? extends Serializable> idList) {
        initEntityClass();
        
        if (idList == null || idList.isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            logger.debug("{} 执行selectBatchIds查询，实体类: {}, ID数量: {}", 
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), idList.size());
            
            // 构建按ID列表查询的QueryWrapper
            QueryWrapper<T> queryWrapper = new QueryWrapper<>();
            queryWrapper.setEntityClass(entityClass);
            queryWrapper.in(tableMetadata.getPrimaryKeyField(), idList);
            
            return selectList(queryWrapper);
            
        } catch (Exception e) {
            logger.error("{} selectBatchIds查询失败，实体类: {}", 
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("selectBatchIds查询失败: " + e.getMessage(), e);
        }
    }

    @Override
    public PageInfo<T> selectPage(int currentPage, int pageSize, QueryWrapper<T> queryWrapper) {
        initEntityClass();
        
        if (queryWrapper == null) {
            queryWrapper = new QueryWrapper<>();
        }
        
        // 参数校验
        if (currentPage < 1) {
            currentPage = OrmConstants.DEFAULT_CURRENT_PAGE;
        }
        if (pageSize < 1) {
            pageSize = OrmConstants.DEFAULT_PAGE_SIZE;
        }
        if (pageSize > OrmConstants.MAX_PAGE_SIZE) {
            pageSize = OrmConstants.MAX_PAGE_SIZE;
        }
        
        try {
            logger.debug("{} 执行selectPage查询，实体类: {}, 页码: {}, 页大小: {}", 
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), currentPage, pageSize);
            
            // 设置实体类到QueryWrapper
            queryWrapper.setEntityClass(entityClass);
            
            // 提取SQL和参数
            QueryWrapperSqlUtils.CompleteSqlResult sqlResult = QueryWrapperSqlUtils.extractCompleteSql(queryWrapper);
            
            // 构建完整的SELECT SQL
            String selectSql = buildSelectSql(sqlResult.getSql(), sqlResult.getTableName());
            
            // 执行分页查询
            List<Map<String, Object>> resultList = sqlExecutor.executePageQuery(
                    selectSql, sqlResult.getParams(), SourceType.EXTERNAL, currentPage, pageSize);
            
            // 映射结果
            List<T> entityList = resultMapper.mapToEntityList(resultList, entityClass);
            
            // 查询总数（用于计算总页数）
            long totalCount = selectCount(queryWrapper);
            int totalPages = (int) Math.ceil((double) totalCount / pageSize);
            
            return new PageInfo<>(currentPage, totalPages, pageSize, entityList);
            
        } catch (Exception e) {
            logger.error("{} selectPage查询失败，实体类: {}", 
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("selectPage查询失败: " + e.getMessage(), e);
        }
    }

    @Override
    public long selectCount(QueryWrapper<T> queryWrapper) {
        initEntityClass();
        
        if (queryWrapper == null) {
            queryWrapper = new QueryWrapper<>();
        }
        
        try {
            logger.debug("{} 执行selectCount查询，实体类: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName());
            
            // 设置实体类到QueryWrapper
            queryWrapper.setEntityClass(entityClass);
            
            // 提取SQL和参数
            QueryWrapperSqlUtils.CompleteSqlResult sqlResult = QueryWrapperSqlUtils.extractCompleteSql(queryWrapper);
            
            // 构建COUNT SQL
            String countSql = buildCountSql(sqlResult.getSql(), sqlResult.getTableName());
            
            // 执行统计查询
            return sqlExecutor.executeCount(countSql, sqlResult.getParams(), SourceType.EXTERNAL);
            
        } catch (Exception e) {
            logger.error("{} selectCount查询失败，实体类: {}", 
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("selectCount查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建SELECT SQL
     */
    private String buildSelectSql(String whereSql, String tableName) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT * FROM ").append(tableName);
        
        if (whereSql != null && !whereSql.trim().isEmpty() && !whereSql.trim().equals("1=1")) {
            if (!whereSql.trim().toUpperCase().startsWith("WHERE")) {
                sql.append(" WHERE ");
            } else {
                sql.append(" ");
            }
            sql.append(whereSql);
        }
        
        return sql.toString();
    }

    /**
     * 构建COUNT SQL
     */
    private String buildCountSql(String whereSql, String tableName) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) FROM ").append(tableName);

        if (whereSql != null && !whereSql.trim().isEmpty() && !whereSql.trim().equals("1=1")) {
            if (!whereSql.trim().toUpperCase().startsWith("WHERE")) {
                sql.append(" WHERE ");
            } else {
                sql.append(" ");
            }
            sql.append(whereSql);
        }

        return sql.toString();
    }

    @Override
    public int insert(T entity) {
        initEntityClass();

        if (entity == null) {
            throw new OrmException.SqlExecutionException("插入实体不能为null");
        }

        try {
            logger.debug("{} 执行insert操作，实体类: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName());

            // 将实体转换为Map
            Map<String, Object> entityMap = resultMapper.mapToMap(entity);

            // 构建INSERT SQL
            String insertSql = buildInsertSql(entityMap);
            List<Object> params = buildInsertParams(entityMap);

            // 执行插入
            return sqlExecutor.executeUpdate(insertSql, params, SourceType.EXTERNAL);

        } catch (Exception e) {
            logger.error("{} insert操作失败，实体类: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("insert操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int insertBatch(List<T> entityList) {
        initEntityClass();

        if (entityList == null || entityList.isEmpty()) {
            return 0;
        }

        try {
            logger.debug("{} 执行insertBatch操作，实体类: {}, 数量: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), entityList.size());

            List<String> sqlList = new ArrayList<>();
            List<List<Object>> paramsList = new ArrayList<>();

            for (T entity : entityList) {
                if (entity != null) {
                    Map<String, Object> entityMap = resultMapper.mapToMap(entity);
                    String insertSql = buildInsertSql(entityMap);
                    List<Object> params = buildInsertParams(entityMap);

                    sqlList.add(insertSql);
                    paramsList.add(params);
                }
            }

            // 执行批量插入
            int[] results = sqlExecutor.executeBatchUpdate(sqlList, paramsList, SourceType.EXTERNAL);

            // 计算总影响行数
            int totalAffected = 0;
            for (int result : results) {
                totalAffected += result;
            }

            return totalAffected;

        } catch (Exception e) {
            logger.error("{} insertBatch操作失败，实体类: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("insertBatch操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int updateById(T entity) {
        initEntityClass();

        if (entity == null) {
            throw new OrmException.SqlExecutionException("更新实体不能为null");
        }

        try {
            logger.debug("{} 执行updateById操作，实体类: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName());

            // 获取主键值
            Field primaryKeyField = tableMetadata.getField(tableMetadata.getPrimaryKeyField());
            Object primaryKeyValue = primaryKeyField.get(entity);

            if (primaryKeyValue == null) {
                throw new OrmException.SqlExecutionException("主键值不能为null");
            }

            // 将实体转换为Map
            Map<String, Object> entityMap = resultMapper.mapToMap(entity);

            // 构建UPDATE SQL
            String updateSql = buildUpdateSql(entityMap, tableMetadata.getPrimaryKeyColumn());
            List<Object> params = buildUpdateParams(entityMap, primaryKeyValue, tableMetadata.getPrimaryKeyColumn());

            // 执行更新
            return sqlExecutor.executeUpdate(updateSql, params, SourceType.EXTERNAL);

        } catch (Exception e) {
            logger.error("{} updateById操作失败，实体类: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("updateById操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int update(T entity, QueryWrapper<T> queryWrapper) {
        initEntityClass();

        if (entity == null) {
            throw new OrmException.SqlExecutionException("更新实体不能为null");
        }
        if (queryWrapper == null) {
            throw new OrmException.SqlExecutionException("更新条件不能为null");
        }

        try {
            logger.debug("{} 执行update操作，实体类: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName());

            // 设置实体类到QueryWrapper
            queryWrapper.setEntityClass(entityClass);

            // 将实体转换为Map
            Map<String, Object> entityMap = resultMapper.mapToMap(entity);

            // 提取WHERE条件
            QueryWrapperSqlUtils.CompleteSqlResult sqlResult = QueryWrapperSqlUtils.extractCompleteSql(queryWrapper);

            // 构建UPDATE SQL
            String updateSql = buildUpdateWithWhereSql(entityMap, sqlResult.getSql());
            List<Object> params = buildUpdateWithWhereParams(entityMap, sqlResult.getParams());

            // 执行更新
            return sqlExecutor.executeUpdate(updateSql, params, SourceType.EXTERNAL);

        } catch (Exception e) {
            logger.error("{} update操作失败，实体类: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("update操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int updateBatchById(List<T> entityList) {
        initEntityClass();

        if (entityList == null || entityList.isEmpty()) {
            return 0;
        }

        try {
            logger.debug("{} 执行updateBatchById操作，实体类: {}, 数量: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), entityList.size());

            List<String> sqlList = new ArrayList<>();
            List<List<Object>> paramsList = new ArrayList<>();

            for (T entity : entityList) {
                if (entity != null) {
                    // 获取主键值
                    Field primaryKeyField = tableMetadata.getField(tableMetadata.getPrimaryKeyField());
                    Object primaryKeyValue = primaryKeyField.get(entity);

                    if (primaryKeyValue != null) {
                        Map<String, Object> entityMap = resultMapper.mapToMap(entity);
                        String updateSql = buildUpdateSql(entityMap, tableMetadata.getPrimaryKeyColumn());
                        List<Object> params = buildUpdateParams(entityMap, primaryKeyValue, tableMetadata.getPrimaryKeyColumn());

                        sqlList.add(updateSql);
                        paramsList.add(params);
                    }
                }
            }

            // 执行批量更新
            int[] results = sqlExecutor.executeBatchUpdate(sqlList, paramsList, SourceType.EXTERNAL);

            // 计算总影响行数
            int totalAffected = 0;
            for (int result : results) {
                totalAffected += result;
            }

            return totalAffected;

        } catch (Exception e) {
            logger.error("{} updateBatchById操作失败，实体类: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("updateBatchById操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int deleteById(Serializable id) {
        initEntityClass();

        if (id == null) {
            return 0;
        }

        try {
            logger.debug("{} 执行deleteById操作，实体类: {}, ID: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), id);

            // 构建DELETE SQL
            String deleteSql = buildDeleteSql(tableMetadata.getPrimaryKeyColumn());
            List<Object> params = new ArrayList<>();
            params.add(id);

            // 执行删除
            return sqlExecutor.executeUpdate(deleteSql, params, SourceType.EXTERNAL);

        } catch (Exception e) {
            logger.error("{} deleteById操作失败，实体类: {}, ID: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), id, e);
            throw new OrmException.SqlExecutionException("deleteById操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int deleteBatchIds(List<? extends Serializable> idList) {
        initEntityClass();

        if (idList == null || idList.isEmpty()) {
            return 0;
        }

        try {
            logger.debug("{} 执行deleteBatchIds操作，实体类: {}, ID数量: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), idList.size());

            // 构建批量删除的QueryWrapper
            QueryWrapper<T> queryWrapper = new QueryWrapper<>();
            queryWrapper.setEntityClass(entityClass);
            queryWrapper.in(tableMetadata.getPrimaryKeyField(), idList);

            return delete(queryWrapper);

        } catch (Exception e) {
            logger.error("{} deleteBatchIds操作失败，实体类: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("deleteBatchIds操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    public int delete(QueryWrapper<T> queryWrapper) {
        initEntityClass();

        if (queryWrapper == null) {
            throw new OrmException.SqlExecutionException("删除条件不能为null");
        }

        try {
            logger.debug("{} 执行delete操作，实体类: {}", OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName());

            // 设置实体类到QueryWrapper
            queryWrapper.setEntityClass(entityClass);

            // 提取WHERE条件
            QueryWrapperSqlUtils.CompleteSqlResult sqlResult = QueryWrapperSqlUtils.extractCompleteSql(queryWrapper);

            // 构建DELETE SQL
            String deleteSql = buildDeleteWithWhereSql(sqlResult.getSql());

            // 执行删除
            return sqlExecutor.executeUpdate(deleteSql, sqlResult.getParams(), SourceType.EXTERNAL);

        } catch (Exception e) {
            logger.error("{} delete操作失败，实体类: {}",
                    OrmConstants.Log.SQL_EXECUTION_PREFIX, entityClass.getName(), e);
            throw new OrmException.SqlExecutionException("delete操作失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean exists(QueryWrapper<T> queryWrapper) {
        return selectCount(queryWrapper) > 0;
    }

    @Override
    public boolean existsById(Serializable id) {
        return selectById(id) != null;
    }

    /**
     * 构建INSERT SQL
     */
    private String buildInsertSql(Map<String, Object> entityMap) {
        StringBuilder sql = new StringBuilder();
        StringBuilder columns = new StringBuilder();
        StringBuilder values = new StringBuilder();

        sql.append("INSERT INTO ").append(tableMetadata.getTableName()).append(" (");

        boolean first = true;
        for (String column : entityMap.keySet()) {
            if (!first) {
                columns.append(", ");
                values.append(", ");
            }
            columns.append(column);
            values.append("?");
            first = false;
        }

        sql.append(columns).append(") VALUES (").append(values).append(")");
        return sql.toString();
    }

    /**
     * 构建INSERT参数
     */
    private List<Object> buildInsertParams(Map<String, Object> entityMap) {
        return new ArrayList<>(entityMap.values());
    }

    /**
     * 构建UPDATE SQL
     */
    private String buildUpdateSql(Map<String, Object> entityMap, String primaryKeyColumn) {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(tableMetadata.getTableName()).append(" SET ");

        boolean first = true;
        for (String column : entityMap.keySet()) {
            if (!column.equals(primaryKeyColumn)) {
                if (!first) {
                    sql.append(", ");
                }
                sql.append(column).append(" = ?");
                first = false;
            }
        }

        sql.append(" WHERE ").append(primaryKeyColumn).append(" = ?");
        return sql.toString();
    }

    /**
     * 构建UPDATE参数
     */
    private List<Object> buildUpdateParams(Map<String, Object> entityMap, Object primaryKeyValue, String primaryKeyColumn) {
        List<Object> params = new ArrayList<>();

        for (Map.Entry<String, Object> entry : entityMap.entrySet()) {
            if (!entry.getKey().equals(primaryKeyColumn)) {
                params.add(entry.getValue());
            }
        }

        params.add(primaryKeyValue);
        return params;
    }

    /**
     * 构建带WHERE条件的UPDATE SQL
     */
    private String buildUpdateWithWhereSql(Map<String, Object> entityMap, String whereSql) {
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(tableMetadata.getTableName()).append(" SET ");

        boolean first = true;
        for (String column : entityMap.keySet()) {
            if (!first) {
                sql.append(", ");
            }
            sql.append(column).append(" = ?");
            first = false;
        }

        if (whereSql != null && !whereSql.trim().isEmpty() && !whereSql.trim().equals("1=1")) {
            if (!whereSql.trim().toUpperCase().startsWith("WHERE")) {
                sql.append(" WHERE ");
            } else {
                sql.append(" ");
            }
            sql.append(whereSql);
        }

        return sql.toString();
    }

    /**
     * 构建带WHERE条件的UPDATE参数
     */
    private List<Object> buildUpdateWithWhereParams(Map<String, Object> entityMap, List<Object> whereParams) {
        List<Object> params = new ArrayList<>(entityMap.values());
        if (whereParams != null) {
            params.addAll(whereParams);
        }
        return params;
    }

    /**
     * 构建DELETE SQL
     */
    private String buildDeleteSql(String primaryKeyColumn) {
        return "DELETE FROM " + tableMetadata.getTableName() + " WHERE " + primaryKeyColumn + " = ?";
    }

    /**
     * 构建带WHERE条件的DELETE SQL
     */
    private String buildDeleteWithWhereSql(String whereSql) {
        StringBuilder sql = new StringBuilder();
        sql.append("DELETE FROM ").append(tableMetadata.getTableName());

        if (whereSql != null && !whereSql.trim().isEmpty() && !whereSql.trim().equals("1=1")) {
            if (!whereSql.trim().toUpperCase().startsWith("WHERE")) {
                sql.append(" WHERE ");
            } else {
                sql.append(" ");
            }
            sql.append(whereSql);
        }

        return sql.toString();
    }
}
