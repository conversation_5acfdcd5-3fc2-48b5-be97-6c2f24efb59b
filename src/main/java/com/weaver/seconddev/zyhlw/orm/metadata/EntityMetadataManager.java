package com.weaver.seconddev.zyhlw.orm.metadata;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import com.weaver.seconddev.zyhlw.orm.exception.OrmException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Arrays;
import java.util.List;

/**
 * 实体元数据管理器，用于管理实体类元数据信息
 * 解析@TableName和@TableField注解，缓存表名和字段映射关系
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
@Component
public class EntityMetadataManager {

    private static final Logger logger = LoggerFactory.getLogger(EntityMetadataManager.class);

    /**
     * 获取实体类的表元数据
     *
     * @param entityClass 实体类
     * @return 表元数据
     */
    public TableMetadata getTableMetadata(Class<?> entityClass) {
        if (entityClass == null) {
            throw new OrmException.MetadataException("实体类不能为null");
        }

        // 先从缓存中获取
        TableMetadata metadata = TableMetadataCache.getByEntityClass(entityClass);
        if (metadata != null) {
            return metadata;
        }

        // 缓存中不存在，解析实体类
        metadata = parseEntityClass(entityClass);
        
        // 缓存解析结果
        TableMetadataCache.put(metadata);
        
        return metadata;
    }

    /**
     * 根据表名获取表元数据
     *
     * @param tableName 表名
     * @return 表元数据，如果不存在返回null
     */
    public TableMetadata getTableMetadataByTableName(String tableName) {
        return TableMetadataCache.getByTableName(tableName);
    }

    /**
     * 获取实体类的表名
     *
     * @param entityClass 实体类
     * @return 表名
     */
    public String getTableName(Class<?> entityClass) {
        TableMetadata metadata = getTableMetadata(entityClass);
        return metadata.getTableName();
    }

    /**
     * 获取实体类的主键字段名
     *
     * @param entityClass 实体类
     * @return 主键字段名
     */
    public String getPrimaryKeyField(Class<?> entityClass) {
        TableMetadata metadata = getTableMetadata(entityClass);
        return metadata.getPrimaryKeyField();
    }

    /**
     * 获取实体类的主键数据库字段名
     *
     * @param entityClass 实体类
     * @return 主键数据库字段名
     */
    public String getPrimaryKeyColumn(Class<?> entityClass) {
        TableMetadata metadata = getTableMetadata(entityClass);
        return metadata.getPrimaryKeyColumn();
    }

    /**
     * 解析实体类，生成表元数据
     *
     * @param entityClass 实体类
     * @return 表元数据
     */
    private TableMetadata parseEntityClass(Class<?> entityClass) {
        logger.debug("{} 开始解析实体类: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, entityClass.getName());

        // 获取表名
        String tableName = extractTableName(entityClass);
        
        // 创建表元数据对象
        TableMetadata metadata = new TableMetadata(entityClass, tableName);

        // 解析字段
        parseFields(entityClass, metadata);

        logger.debug("{} 完成解析实体类: {}, 表名: {}, 字段数: {}", 
                OrmConstants.Log.ENTITY_MAPPING_PREFIX, 
                entityClass.getName(), 
                tableName, 
                metadata.getFieldToColumnMap().size());

        return metadata;
    }

    /**
     * 提取表名
     *
     * @param entityClass 实体类
     * @return 表名
     */
    private String extractTableName(Class<?> entityClass) {
        TableName tableNameAnnotation = entityClass.getAnnotation(TableName.class);
        if (tableNameAnnotation != null && !tableNameAnnotation.value().trim().isEmpty()) {
            return tableNameAnnotation.value().trim();
        }

        // 如果没有@TableName注解，使用类名转换为下划线格式
        String className = entityClass.getSimpleName();
        if (className.endsWith("DO")) {
            className = className.substring(0, className.length() - 2);
        }
        return camelToUnderscore(className);
    }

    /**
     * 解析字段
     *
     * @param entityClass 实体类
     * @param metadata    表元数据
     */
    private void parseFields(Class<?> entityClass, TableMetadata metadata) {
        // 获取所有字段，包括父类字段
        List<Field> allFields = getAllFields(entityClass);

        for (Field field : allFields) {
            // 跳过静态字段和final字段
            if (Modifier.isStatic(field.getModifiers()) || Modifier.isFinal(field.getModifiers())) {
                continue;
            }

            // 检查是否有@TableField注解且exist=false
            TableField tableField = field.getAnnotation(TableField.class);
            if (tableField != null && !tableField.exist()) {
                continue;
            }

            String fieldName = field.getName();
            String columnName = extractColumnName(field);
            Class<?> fieldType = field.getType();

            // 设置字段可访问
            field.setAccessible(true);

            // 添加字段映射
            metadata.addFieldMapping(fieldName, columnName, fieldType, field);

            // 检查是否为主键字段
            if (field.isAnnotationPresent(TableId.class) || "id".equals(fieldName)) {
                metadata.setPrimaryKeyField(fieldName);
                metadata.setPrimaryKeyColumn(columnName);
            }

            // 检查是否为逻辑删除字段
            if ("deleteType".equals(fieldName)) {
                metadata.setHasLogicDelete(true);
                metadata.setLogicDeleteField(fieldName);
                metadata.setLogicDeleteColumn(columnName);
            }

            // 检查是否为租户字段
            if ("tenantKey".equals(fieldName)) {
                metadata.setHasTenant(true);
                metadata.setTenantField(fieldName);
                metadata.setTenantColumn(columnName);
            }
        }

        // 如果没有找到主键字段，默认使用id
        if (metadata.getPrimaryKeyField() == null) {
            metadata.setPrimaryKeyField(OrmConstants.PRIMARY_KEY_FIELD);
            metadata.setPrimaryKeyColumn(OrmConstants.PRIMARY_KEY_FIELD);
        }
    }

    /**
     * 提取字段的数据库列名
     *
     * @param field 字段
     * @return 数据库列名
     */
    private String extractColumnName(Field field) {
        TableField tableField = field.getAnnotation(TableField.class);
        if (tableField != null && !tableField.value().trim().isEmpty()) {
            return tableField.value().trim();
        }

        // 如果没有@TableField注解，使用字段名转换为下划线格式
        return camelToUnderscore(field.getName());
    }

    /**
     * 获取所有字段，包括父类字段
     *
     * @param clazz 类
     * @return 所有字段列表
     */
    private List<Field> getAllFields(Class<?> clazz) {
        java.util.List<Field> fields = new java.util.ArrayList<>();
        
        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        
        return fields;
    }

    /**
     * 驼峰命名转下划线命名
     *
     * @param camelCase 驼峰命名字符串
     * @return 下划线命名字符串
     */
    private String camelToUnderscore(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }

        StringBuilder result = new StringBuilder();
        for (int i = 0; i < camelCase.length(); i++) {
            char c = camelCase.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i > 0) {
                    result.append(OrmConstants.DB_FIELD_SEPARATOR);
                }
                result.append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 下划线命名转驼峰命名
     *
     * @param underscore 下划线命名字符串
     * @return 驼峰命名字符串
     */
    public String underscoreToCamel(String underscore) {
        if (underscore == null || underscore.isEmpty()) {
            return underscore;
        }

        StringBuilder result = new StringBuilder();
        boolean nextUpperCase = false;
        
        for (int i = 0; i < underscore.length(); i++) {
            char c = underscore.charAt(i);
            if (c == '_') {
                nextUpperCase = true;
            } else {
                if (nextUpperCase) {
                    result.append(Character.toUpperCase(c));
                    nextUpperCase = false;
                } else {
                    result.append(c);
                }
            }
        }
        
        return result.toString();
    }

    /**
     * 清空缓存
     */
    public void clearCache() {
        TableMetadataCache.clear();
        logger.info("{} 清空实体元数据缓存", OrmConstants.Log.CACHE_OPERATION_PREFIX);
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        return TableMetadataCache.getCacheStats();
    }
}
