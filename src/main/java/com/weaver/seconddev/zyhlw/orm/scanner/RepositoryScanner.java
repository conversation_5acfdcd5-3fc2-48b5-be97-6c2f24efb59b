package com.weaver.seconddev.zyhlw.orm.scanner;

import com.weaver.seconddev.zyhlw.orm.annotation.Repository;
import com.weaver.seconddev.zyhlw.orm.constant.OrmConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanDefinitionHolder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.context.annotation.ClassPathBeanDefinitionScanner;
import org.springframework.core.type.filter.AnnotationTypeFilter;

import java.util.Set;

/**
 * Repository扫描器，用于扫描和注册@Repository注解的接口
 * 自动创建代理实例并注册为Spring Bean
 *
 * <AUTHOR>
 * @date 2025/6/27
 */
public class RepositoryScanner extends ClassPathBeanDefinitionScanner {

    private static final Logger logger = LoggerFactory.getLogger(RepositoryScanner.class);

    /**
     * Repository代理工厂Bean名称
     */
    private String repositoryProxyBeanName = "repositoryProxy";

    public RepositoryScanner(BeanDefinitionRegistry registry) {
        super(registry, false);
        registerFilters();
    }

    /**
     * 注册过滤器，只扫描@Repository注解的接口
     */
    public void registerFilters() {
        // 添加@Repository注解过滤器
        addIncludeFilter(new AnnotationTypeFilter(Repository.class));
        
        // 排除非接口类型
        addExcludeFilter((metadataReader, metadataReaderFactory) -> {
            String className = metadataReader.getClassMetadata().getClassName();
            try {
                Class<?> clazz = Class.forName(className);
                return !clazz.isInterface();
            } catch (ClassNotFoundException e) {
                logger.warn("{} 无法加载类: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, className);
                return true;
            }
        });
    }

    @Override
    public Set<BeanDefinitionHolder> doScan(String... basePackages) {
        Set<BeanDefinitionHolder> beanDefinitions = super.doScan(basePackages);
        
        if (beanDefinitions.isEmpty()) {
            logger.warn("{} 未找到任何@Repository注解的接口", OrmConstants.Log.ENTITY_MAPPING_PREFIX);
        } else {
            processBeanDefinitions(beanDefinitions);
        }
        
        return beanDefinitions;
    }

    /**
     * 处理Bean定义，将Repository接口转换为代理Bean
     */
    private void processBeanDefinitions(Set<BeanDefinitionHolder> beanDefinitions) {
        for (BeanDefinitionHolder holder : beanDefinitions) {
            GenericBeanDefinition definition = (GenericBeanDefinition) holder.getBeanDefinition();
            String beanClassName = definition.getBeanClassName();
            
            logger.debug("{} 处理Repository接口: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, beanClassName);
            
            // 设置Bean工厂方法
            definition.setBeanClass(RepositoryFactoryBean.class);
            definition.getConstructorArgumentValues().addGenericArgumentValue(beanClassName);
            definition.getPropertyValues().add("repositoryProxy", repositoryProxyBeanName);
            
            // 设置为单例
            definition.setScope(BeanDefinition.SCOPE_SINGLETON);
            
            logger.debug("{} Repository接口处理完成: {}", OrmConstants.Log.ENTITY_MAPPING_PREFIX, beanClassName);
        }
    }

    @Override
    protected boolean isCandidateComponent(AnnotatedBeanDefinition beanDefinition) {
        return beanDefinition.getMetadata().isInterface() && beanDefinition.getMetadata().isIndependent();
    }

    /**
     * 设置Repository代理Bean名称
     */
    public void setRepositoryProxyBeanName(String repositoryProxyBeanName) {
        this.repositoryProxyBeanName = repositoryProxyBeanName;
    }
}
